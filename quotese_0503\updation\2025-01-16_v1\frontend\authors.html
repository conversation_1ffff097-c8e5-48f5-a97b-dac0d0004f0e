<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- 基础SEO标签 - 将由MetaTags.js动态更新 -->
    <title data-i18n="page.authors.title">名人作者 - QuoteSe</title>
    <meta name="description" content="" data-i18n-content="page.authors.description">
    <meta name="keywords" content="名人,作者,名言,警句,智慧">
    <meta name="author" content="QuoteSe Team">
    <meta name="robots" content="index,follow">
    
    <!-- Canonical链接 - 将由Router.js动态更新 -->
    <link rel="canonical" href="">
    
    <!-- 语言相关标签 - 将由Router.js动态更新 -->
    <link rel="alternate" hreflang="zh-CN" href="">
    <link rel="alternate" hreflang="en-US" href="">
    <link rel="alternate" hreflang="x-default" href="">
    
    <!-- Open Graph标签 - 将由MetaTags.js动态更新 -->
    <meta property="og:title" content="">
    <meta property="og:description" content="">
    <meta property="og:type" content="website">
    <meta property="og:url" content="">
    <meta property="og:site_name" content="QuoteSe">
    <meta property="og:locale" content="zh_CN">
    <meta property="og:image" content="/images/og-authors.jpg">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    
    <!-- Twitter Card标签 - 将由MetaTags.js动态更新 -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@quotese">
    <meta name="twitter:title" content="">
    <meta name="twitter:description" content="">
    <meta name="twitter:image" content="/images/og-authors.jpg">
    
    <!-- 样式表 -->
    <link href="https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif:wght@400;500;600;700&family=Noto+Sans:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="css/styles.css" rel="stylesheet">
    <link href="css/animations.css" rel="stylesheet">
    
    <!-- 主题颜色 -->
    <meta name="theme-color" content="#2c3e50">
    <meta name="msapplication-TileColor" content="#2c3e50">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    
    <!-- 分析脚本 -->
    <script type="text/javascript">
        (function(c,l,a,r,i,t,y){
            c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
            t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
            y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
        })(window, document, "clarity", "script", "rxa7t53bmr");
    </script>
    <script src="js/analytics.js"></script>
</head>
<body class="light-mode">
    <!-- 跳转到主要内容的链接（无障碍） -->
    <a href="#main-content" class="skip-link" data-i18n="a11y.skip_to_content">跳转到主要内容</a>
    
    <!-- 导航栏 -->
    <header id="navigation-container" role="banner"></header>
    
    <!-- 主要内容 -->
    <main id="main-content" class="container mx-auto px-4 py-8" role="main">
        <!-- 面包屑导航 -->
        <nav id="breadcrumb-container" aria-label="面包屑导航"></nav>
        
        <!-- 页面标题区域 -->
        <section class="mb-8 text-center py-4" id="page-header">
            <div class="flex justify-center mb-4 fade-in">
                <div class="w-24 h-24 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-300 border-4 border-blue-400 dark:border-blue-600">
                    <i class="fas fa-users text-3xl" aria-hidden="true"></i>
                </div>
            </div>
            <h1 class="text-4xl md:text-5xl font-bold mb-4 fade-in fade-in-delay-1" data-i18n="page.authors.title">
                名人作者
            </h1>
            <p class="text-gray-500 dark:text-gray-400 fade-in fade-in-delay-2" data-i18n="page.authors.description">
                探索古今中外著名思想家、文学家、哲学家的智慧言论
            </p>
            <div id="authors-count-container" class="text-gray-500 dark:text-gray-400 fade-in fade-in-delay-3">
                <span data-i18n="content.author">作者</span>: <span id="authors-count">0</span>
            </div>
        </section>
        
        <!-- 作者详情区域（当显示单个作者时） -->
        <section class="mb-8 text-center py-4 hidden" id="author-detail-header">
            <div class="flex justify-center mb-4 fade-in">
                <div class="w-32 h-32 rounded-full bg-gradient-to-br from-yellow-100 to-yellow-200 dark:from-yellow-900 dark:to-yellow-800 flex items-center justify-center text-yellow-600 dark:text-yellow-300 border-4 border-yellow-400 dark:border-yellow-600 shadow-lg">
                    <span id="author-initial" class="text-4xl font-bold">A</span>
                </div>
            </div>
            <h1 id="author-name" class="text-4xl md:text-6xl font-bold mb-4 fade-in fade-in-delay-1 bg-gradient-to-r from-gray-900 to-gray-600 dark:from-gray-100 dark:to-gray-400 bg-clip-text text-transparent">
                作者姓名
            </h1>
            <div id="author-meta" class="mb-6 fade-in fade-in-delay-2">
                <p id="author-description" class="text-lg text-gray-600 dark:text-gray-300 mb-4 max-w-2xl mx-auto">
                    作者简介
                </p>
                <div class="flex flex-wrap justify-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                    <span id="author-birth-death" class="hidden">
                        <i class="fas fa-calendar-alt mr-1" aria-hidden="true"></i>
                        <span id="birth-year"></span> - <span id="death-year"></span>
                    </span>
                    <span id="author-nationality" class="hidden">
                        <i class="fas fa-globe mr-1" aria-hidden="true"></i>
                        <span id="nationality"></span>
                    </span>
                    <span id="quote-count-container">
                        <i class="fas fa-quote-left mr-1" aria-hidden="true"></i>
                        <span id="quote-count">0</span> <span data-i18n="content.quotes">名言</span>
                    </span>
                </div>
            </div>
        </section>
        
        <!-- 内容网格布局 -->
        <div class="flex flex-col lg:flex-row gap-8">
            <!-- 左栏（主要内容） -->
            <section class="lg:w-2/3">
                <!-- 筛选和排序控件 -->
                <div id="filter-controls" class="mb-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div class="flex flex-wrap gap-4 items-center">
                        <!-- 搜索框 -->
                        <div class="flex-1 min-w-64">
                            <input type="search" id="author-search" 
                                   class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                                   data-i18n-placeholder="search.placeholder"
                                   placeholder="搜索作者...">
                        </div>
                        
                        <!-- 排序选择 -->
                        <div class="flex items-center gap-2">
                            <label for="sort-select" class="text-sm font-medium text-gray-700 dark:text-gray-300" data-i18n="filter.sort">
                                排序:
                            </label>
                            <select id="sort-select" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white">
                                <option value="name" data-i18n="sort.alphabetical">字母顺序</option>
                                <option value="quote_count" data-i18n="sort.popular">名言数量</option>
                                <option value="random" data-i18n="sort.random">随机排序</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <!-- 作者列表/名言列表容器 -->
                <div id="content-container">
                    <!-- 作者列表组件将在这里加载 -->
                    <div id="authors-list-container">
                        <!-- Authors list component will be loaded here -->
                    </div>
                    
                    <!-- 名言列表组件将在这里加载（当显示作者详情时） -->
                    <div id="quotes-container" class="hidden">
                        <!-- Quotes list component will be loaded here -->
                    </div>
                </div>
                
                <!-- 分页组件 -->
                <div id="pagination-container" class="mt-8">
                    <!-- Pagination component will be loaded here -->
                </div>
            </section>
            
            <!-- 右栏（侧边栏） -->
            <aside class="lg:w-1/3" role="complementary" aria-label="侧边栏">
                <!-- 热门主题组件 -->
                <div id="popular-topics-container" class="mb-6">
                    <!-- Popular topics component will be loaded here -->
                </div>
                
                <!-- 相关作者（当显示作者详情时） -->
                <div id="related-authors-container" class="hidden">
                    <!-- Related authors will be loaded here -->
                </div>
                
                <!-- 语言切换器 -->
                <div id="sidebar-language-switcher" class="mb-6">
                    <!-- Language switcher component will be loaded here -->
                </div>
            </aside>
        </div>
    </main>
    
    <!-- 页脚 -->
    <footer id="footer-container" role="contentinfo"></footer>
    
    <!-- JavaScript模块 -->
    <!-- 配置和核心模块 -->
    <script src="js/config.js"></script>
    <script src="js/debug.js"></script>
    
    <!-- 国际化支持 -->
    <script src="js/i18n/i18n.js"></script>
    <script src="js/i18n/zh.js"></script>
    <script src="js/i18n/en.js"></script>
    
    <!-- 核心功能模块 -->
    <script src="js/url-handler.js"></script>
    <script src="js/core/router.js"></script>
    <script src="js/seo/meta-tags.js"></script>
    <script src="js/seo/structured-data.js"></script>
    
    <!-- 组件系统 -->
    <script src="js/component-loader.js"></script>
    <script src="js/components/navigation.js"></script>
    <script src="js/components/breadcrumb.js"></script>
    <script src="js/components/language-switcher.js"></script>
    <script src="js/components/pagination.js"></script>
    <script src="js/components/quote-card.js"></script>
    
    <!-- API和数据 -->
    <script src="js/mock-data.js"></script>
    <script src="js/api-client.js"></script>
    
    <!-- 功能模块 -->
    <script src="js/theme.js"></script>
    <script src="js/mobile-menu.js"></script>
    <script src="js/social-meta.js"></script>
    <script src="js/analytics.js"></script>
    
    <!-- 全局修复脚本 -->
    <script src="js/global-fix.js"></script>
    
    <!-- 页面特定脚本 -->
    <script src="js/pages/authors.js"></script>
</body>
</html>
