/**
 * 网站配置 - SEO优化版本 2025-01-16
 * 包含不同环境下的配置参数和SEO相关设置
 */

const Config = {
    // 开发环境配置
    development: {
        apiEndpoint: 'http://127.0.0.1:8001/api/',
        useMockData: false,
        debug: true,
        baseUrl: 'http://localhost:3000',
        enableSEO: false
    },

    // 测试环境配置
    testing: {
        apiEndpoint: 'http://test-server.example.com/api/',
        useMockData: false,
        debug: true,
        baseUrl: 'http://test.quotese.com',
        enableSEO: true
    },

    // 生产环境配置
    production: {
        apiEndpoint: 'https://api.quotese.com/api/',
        useMockData: false,
        debug: false,
        baseUrl: 'https://quotese.com',
        enableSEO: true
    },

    // SEO相关配置
    seo: {
        siteName: 'QuoteSe',
        siteDescription: '收集世界各地的名言警句，传播智慧与哲理',
        defaultKeywords: '名言,警句,格言,智慧,哲理,人生感悟',
        author: 'QuoteSe Team',
        twitterHandle: '@quotese',
        facebookPage: 'https://facebook.com/quotese',
        defaultImage: '/images/og-default.jpg',
        favicon: '/favicon.ico'
    },

    // 多语言配置
    i18n: {
        defaultLanguage: 'zh',
        supportedLanguages: ['zh', 'en'],
        fallbackLanguage: 'zh',
        autoDetect: true,
        cookieName: 'quotese_lang',
        cookieExpiry: 365 // 天数
    },

    // URL配置
    urls: {
        useTrailingSlash: true,
        enablePrettyUrls: true,
        enableLanguagePrefix: true,
        maxSlugLength: 100,
        slugSeparator: '-'
    },

    // 分页配置
    pagination: {
        defaultPageSize: 20,
        maxPageSize: 100,
        showPageNumbers: 5
    },

    // 搜索配置
    search: {
        minQueryLength: 2,
        maxQueryLength: 100,
        enableAutoComplete: true,
        enableSearchSuggestions: true,
        maxSuggestions: 10
    },

    // 性能配置
    performance: {
        enableLazyLoading: true,
        enableImageOptimization: true,
        enableCaching: true,
        cacheExpiry: 3600000, // 1小时（毫秒）
        enableCompression: true
    },

    // 分析配置
    analytics: {
        enableGoogleAnalytics: true,
        googleAnalyticsId: 'GA_MEASUREMENT_ID',
        enableBaiduAnalytics: true,
        baiduAnalyticsId: 'BAIDU_ANALYTICS_ID'
    },

    // 获取当前环境配置
    getCurrent: function() {
        const hostname = window.location.hostname;

        if (hostname === 'localhost' || hostname === '127.0.0.1') {
            return { ...this.development, ...this.getCommonConfig() };
        } else if (hostname.includes('test')) {
            return { ...this.testing, ...this.getCommonConfig() };
        } else {
            return { ...this.production, ...this.getCommonConfig() };
        }
    },

    // 获取通用配置
    getCommonConfig: function() {
        return {
            seo: this.seo,
            i18n: this.i18n,
            urls: this.urls,
            pagination: this.pagination,
            search: this.search,
            performance: this.performance,
            analytics: this.analytics
        };
    },

    // 获取SEO配置
    getSEOConfig: function() {
        return this.seo;
    },

    // 获取国际化配置
    getI18nConfig: function() {
        return this.i18n;
    },

    // 获取URL配置
    getUrlConfig: function() {
        return this.urls;
    }
};

// 导出当前环境配置
window.AppConfig = Config.getCurrent();
