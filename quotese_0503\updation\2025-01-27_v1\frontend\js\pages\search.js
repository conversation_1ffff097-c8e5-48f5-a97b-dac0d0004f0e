/**
 * 搜索页面脚本
 * 创建日期: 2025-01-27
 * 说明: 负责处理搜索功能和结果展示，包括API调用、分页、关键词高亮等功能
 */

class SearchPage {
    constructor() {
        this.currentQuery = '';
        this.currentPage = 1;
        this.pageSize = 20;
        this.isLoading = false;
        this.totalResults = 0;
        
        this.init();
    }

    /**
     * 初始化搜索页面
     * 2025-01-27: 搜索页面初始化逻辑，包含URL参数解析和组件初始化
     */
    init() {
        // 从URL获取搜索查询
        this.currentQuery = UrlHandler.getSearchQueryFromUrl() || '';
        
        // 初始化搜索框
        this.initSearchInput();
        
        // 如果有查询参数，执行搜索
        if (this.currentQuery) {
            this.performSearch();
        } else {
            this.showEmptyState();
        }
        
        // 初始化分页
        this.initPagination();
    }

    /**
     * 初始化搜索输入框
     * 2025-01-27: 搜索输入框事件绑定和初始化
     */
    initSearchInput() {
        const searchInput = document.getElementById('search-input');
        const searchButton = document.getElementById('search-button');
        
        if (searchInput) {
            // 设置当前查询值
            searchInput.value = this.currentQuery;
            
            // 监听回车键
            searchInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    this.handleSearch();
                }
            });
            
            // 自动聚焦
            if (!this.currentQuery) {
                searchInput.focus();
            }
        }
        
        if (searchButton) {
            searchButton.addEventListener('click', () => {
                this.handleSearch();
            });
        }
    }

    /**
     * 处理搜索
     * 2025-01-27: 搜索查询处理和URL更新
     */
    handleSearch() {
        const searchInput = document.getElementById('search-input');
        if (!searchInput) return;
        
        const query = searchInput.value.trim();
        if (!query) {
            this.showEmptyState();
            return;
        }
        
        // 更新URL
        UrlHandler.updateQueryParam('q', query);
        
        // 重置页码
        this.currentPage = 1;
        this.currentQuery = query;
        
        // 执行搜索
        this.performSearch();
    }

    /**
     * 执行搜索
     * 2025-01-27: API调用和搜索结果处理
     */
    async performSearch() {
        if (this.isLoading || !this.currentQuery) return;
        
        this.isLoading = true;
        this.showLoading();
        
        try {
            // 调用API搜索
            const result = await window.ApiClient.getQuotes(
                this.currentPage,
                this.pageSize,
                { search: this.currentQuery }
            );
            
            this.totalResults = result.totalCount;
            
            // 显示搜索结果
            this.displayResults(result.quotes, result.totalCount);
            
            // 更新分页
            this.updatePagination(result);
            
        } catch (error) {
            console.error('Search error:', error);
            this.showError('搜索时发生错误，请稍后重试。');
        } finally {
            this.isLoading = false;
        }
    }

    /**
     * 显示搜索结果
     * 2025-01-27: 搜索结果渲染和展示逻辑
     */
    displayResults(quotes, totalCount) {
        const container = document.getElementById('search-results-container');
        const header = document.getElementById('search-results-header');
        
        if (!container || !header) return;
        
        // 更新结果头部
        this.updateResultsHeader(totalCount);
        
        if (quotes.length === 0) {
            this.showNoResults();
            return;
        }
        
        // 生成结果HTML
        const resultsHtml = quotes.map(quote => this.generateQuoteHtml(quote)).join('');
        
        container.innerHTML = resultsHtml;
        
        // 添加点击事件
        this.addQuoteClickEvents();
        
        // 高亮搜索关键词
        this.highlightSearchTerms();
    }

    /**
     * 更新结果头部
     * 2025-01-27: 搜索结果统计信息更新
     */
    updateResultsHeader(totalCount) {
        const titleElement = document.getElementById('search-results-title');
        const countElement = document.getElementById('search-results-count');
        
        if (titleElement) {
            titleElement.textContent = `搜索结果：${this.currentQuery}`;
        }
        
        if (countElement) {
            countElement.textContent = `找到 ${totalCount} 条结果`;
        }
    }

    /**
     * 生成名言HTML
     * 2025-01-27: 搜索结果项HTML生成
     */
    generateQuoteHtml(quote) {
        const author = quote.author ? quote.author.name : '未知作者';
        const categories = quote.categories ? quote.categories.map(cat => cat.name).join(', ') : '';
        const sources = quote.sources ? quote.sources.map(src => src.name).join(', ') : '';
        
        return `
            <div class="search-result-item" data-quote-id="${quote.id}">
                <div class="search-result-quote">
                    "${quote.content}"
                </div>
                <div class="search-result-meta">
                    <span class="search-result-author">— ${author}</span>
                    ${categories ? `
                        <div class="search-result-categories">
                            ${categories.split(', ').map(cat => `<span class="search-result-tag">${cat}</span>`).join('')}
                        </div>
                    ` : ''}
                    ${sources ? `
                        <div class="search-result-sources">
                            ${sources.split(', ').map(src => `<span class="search-result-tag">${src}</span>`).join('')}
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    /**
     * 添加名言点击事件
     * 2025-01-27: 搜索结果点击跳转功能
     */
    addQuoteClickEvents() {
        const quoteItems = document.querySelectorAll('.search-result-item');
        quoteItems.forEach(item => {
            item.addEventListener('click', () => {
                const quoteId = item.dataset.quoteId;
                if (quoteId) {
                    window.location.href = `quote.html?id=${quoteId}`;
                }
            });
        });
    }

    /**
     * 高亮搜索关键词
     * 2025-01-27: 搜索关键词高亮显示功能
     */
    highlightSearchTerms() {
        if (!this.currentQuery) return;
        
        const searchTerms = this.currentQuery.toLowerCase().split(' ').filter(term => term.length > 0);
        const quoteElements = document.querySelectorAll('.search-result-quote');
        
        quoteElements.forEach(element => {
            let content = element.textContent;
            
            searchTerms.forEach(term => {
                const regex = new RegExp(`(${this.escapeRegex(term)})`, 'gi');
                content = content.replace(regex, '<span class="search-highlight">$1</span>');
            });
            
            element.innerHTML = content;
        });
    }

    /**
     * 转义正则表达式特殊字符
     * 2025-01-27: 正则表达式安全处理
     */
    escapeRegex(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    /**
     * 显示加载状态
     * 2025-01-27: 搜索加载状态显示
     */
    showLoading() {
        const container = document.getElementById('search-results-container');
        if (!container) return;
        
        container.innerHTML = `
            <div class="search-loading">
                <div class="search-loading-spinner"></div>
                <p>正在搜索...</p>
            </div>
        `;
    }

    /**
     * 显示无结果状态
     * 2025-01-27: 无搜索结果时的友好提示
     */
    showNoResults() {
        const container = document.getElementById('search-results-container');
        if (!container) return;
        
        container.innerHTML = `
            <div class="no-results">
                <div class="no-results-icon">
                    <i class="fas fa-search"></i>
                </div>
                <h3 class="no-results-title">未找到相关结果</h3>
                <p class="no-results-text">没有找到与 "${this.currentQuery}" 相关的名言。</p>
                
                <div class="no-results-suggestions">
                    <h4>搜索建议：</h4>
                    <ul>
                        <li>检查拼写是否正确</li>
                        <li>尝试使用更简单的关键词</li>
                        <li>尝试搜索作者姓名</li>
                        <li>尝试搜索主题词汇，如"爱情"、"智慧"、"成功"等</li>
                    </ul>
                </div>
            </div>
        `;
    }

    /**
     * 显示空状态
     * 2025-01-27: 初始空状态显示
     */
    showEmptyState() {
        const container = document.getElementById('search-results-container');
        const header = document.getElementById('search-results-header');
        
        if (header) {
            header.style.display = 'none';
        }
        
        if (!container) return;
        
        container.innerHTML = `
            <div class="no-results">
                <div class="no-results-icon">
                    <i class="fas fa-quote-right"></i>
                </div>
                <h3 class="no-results-title">开始搜索名言</h3>
                <p class="no-results-text">在上方搜索框中输入关键词，探索智慧的宝库。</p>
                
                <div class="no-results-suggestions">
                    <h4>搜索示例：</h4>
                    <ul>
                        <li>按内容搜索：如"成功"、"爱情"、"智慧"</li>
                        <li>按作者搜索：如"爱因斯坦"、"孔子"</li>
                        <li>按主题搜索：如"励志"、"哲学"</li>
                    </ul>
                </div>
            </div>
        `;
    }

    /**
     * 显示错误状态
     * 2025-01-27: 搜索错误状态处理
     */
    showError(message) {
        const container = document.getElementById('search-results-container');
        if (!container) return;

        container.innerHTML = `
            <div class="no-results">
                <div class="no-results-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h3 class="no-results-title">搜索出错</h3>
                <p class="no-results-text">${message}</p>
            </div>
        `;
    }

    /**
     * 初始化分页
     * 2025-01-27: 分页组件初始化和配置
     */
    initPagination() {
        // 创建分页组件实例
        this.pagination = new window.PaginationComponent({
            containerId: 'pagination-container',
            onPageChange: (page) => this.handlePageChange(page),
            showInfo: true,
            infoText: 'Showing {start}-{end} of {total} search results'
        });
    }

    /**
     * 更新分页
     * 2025-01-27: 分页状态更新和显示控制
     */
    updatePagination(result) {
        if (!this.pagination) return;

        // 只有在有结果时才显示分页
        const paginationContainer = document.getElementById('pagination-container');
        if (result.totalCount > 0 && result.totalPages > 1) {
            paginationContainer.style.display = 'block';
            this.pagination.update({
                currentPage: result.currentPage,
                totalPages: result.totalPages,
                totalItems: result.totalCount,
                pageSize: result.pageSize
            });
        } else {
            paginationContainer.style.display = 'none';
        }
    }

    /**
     * 处理页码变更
     * 2025-01-27: 分页页码变更处理和页面滚动
     */
    handlePageChange(page) {
        this.currentPage = page;
        this.performSearch();

        // 滚动到页面顶部
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }
}

// 页面加载完成后初始化
// 2025-01-27: 搜索页面自动初始化
document.addEventListener('DOMContentLoaded', function() {
    new SearchPage();
});
