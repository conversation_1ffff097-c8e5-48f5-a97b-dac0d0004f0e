# 启用重写引擎
RewriteEngine On

# 设置基础URL
RewriteBase /

# 处理首页
RewriteRule ^$ index.html [L]

# 处理作者页面
RewriteRule ^authors/?$ authors.html [L]
RewriteRule ^author/([0-9]+)/([^/]+)/?$ author.html?id=$1&name=$2 [L,QSA]

# 处理类别页面
RewriteRule ^categories/?$ categories.html [L]
RewriteRule ^category/([0-9]+)/([^/]+)/?$ category.html?id=$1&name=$2 [L,QSA]

# 处理来源页面
RewriteRule ^sources/?$ sources.html [L]
RewriteRule ^source/([0-9]+)/([^/]+)/?$ source.html?id=$1&name=$2 [L,QSA]

# 处理名言页面
RewriteRule ^quote/([0-9]+)/?$ quote.html?id=$1 [L,QSA]

# 2023-05-10: 修改部分开始
# 功能说明: 添加了搜索页面的URL重写规则
# 搜索页面重写规则
RewriteRule ^search/?$ search.html [L]
RewriteRule ^search/q/([^/]+)/?$ search.html?q=$1 [L,QSA]
# 2023-05-10: 修改部分结束

# 处理404错误
ErrorDocument 404 /404.html

# 设置缓存控制
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>

# 压缩文件
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# 禁止目录浏览
Options -Indexes

# 设置默认字符集
AddDefaultCharset UTF-8

# 禁止访问敏感文件
<FilesMatch "(\.(bak|config|dist|fla|inc|ini|log|psd|sh|sql|swp)|~)$">
    Order allow,deny
    Deny from all
    Satisfy All
</FilesMatch>