# SEO优化的URL重写规则 - 2025-01-16
# 实现RESTful风格URL，去除.html扩展名，支持多语言

# 启用重写引擎
RewriteEngine On

# 设置基础URL
RewriteBase /

# 强制使用HTTPS（生产环境启用）
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# ==========================================
# 向后兼容性 - 301重定向旧URL到新URL
# ==========================================

# 重定向旧的作者页面
RewriteCond %{QUERY_STRING} ^id=([0-9]+)&name=([^&]+)$
RewriteRule ^author\.html$ /authors/%2/? [R=301,L]

# 重定向旧的类别页面  
RewriteCond %{QUERY_STRING} ^id=([0-9]+)&name=([^&]+)$
RewriteRule ^category\.html$ /categories/%2/? [R=301,L]

# 重定向旧的来源页面
RewriteCond %{QUERY_STRING} ^id=([0-9]+)&name=([^&]+)$
RewriteRule ^source\.html$ /sources/%2/? [R=301,L]

# 重定向旧的名言页面
RewriteCond %{QUERY_STRING} ^id=([0-9]+)$
RewriteRule ^quote\.html$ /quotes/%1/? [R=301,L]

# 重定向旧的搜索页面
RewriteCond %{QUERY_STRING} ^q=([^&]+)$
RewriteRule ^search\.html$ /search/%1/? [R=301,L]

# ==========================================
# 多语言支持
# ==========================================

# 语言检测和重定向（可选）
# RewriteCond %{HTTP:Accept-Language} ^en [NC]
# RewriteCond %{REQUEST_URI} !^/en/
# RewriteRule ^(.*)$ /en/$1 [R=302,L]

# ==========================================
# 新的RESTful URL结构
# ==========================================

# 首页处理
RewriteRule ^$ index.html [L]
RewriteRule ^home/?$ index.html [L]

# 英文版本首页
RewriteRule ^en/?$ index.html?lang=en [L,QSA]
RewriteRule ^en/home/<USER>

# ==========================================
# 作者相关页面
# ==========================================

# 作者列表页面
RewriteRule ^authors/?$ authors.html [L]
RewriteRule ^en/authors/?$ authors.html?lang=en [L,QSA]

# 作者详情页面 /authors/shakespeare/
RewriteRule ^authors/([^/]+)/?$ authors.html?slug=$1 [L,QSA]
RewriteRule ^en/authors/([^/]+)/?$ authors.html?slug=$1&lang=en [L,QSA]

# 作者名言列表 /authors/shakespeare/quotes/
RewriteRule ^authors/([^/]+)/quotes/?$ authors.html?slug=$1&view=quotes [L,QSA]
RewriteRule ^en/authors/([^/]+)/quotes/?$ authors.html?slug=$1&view=quotes&lang=en [L,QSA]

# ==========================================
# 类别相关页面  
# ==========================================

# 类别列表页面
RewriteRule ^categories/?$ categories.html [L]
RewriteRule ^en/categories/?$ categories.html?lang=en [L,QSA]

# 类别详情页面 /categories/philosophy/
RewriteRule ^categories/([^/]+)/?$ categories.html?slug=$1 [L,QSA]
RewriteRule ^en/categories/([^/]+)/?$ categories.html?slug=$1&lang=en [L,QSA]

# 类别名言列表 /categories/philosophy/quotes/
RewriteRule ^categories/([^/]+)/quotes/?$ categories.html?slug=$1&view=quotes [L,QSA]
RewriteRule ^en/categories/([^/]+)/quotes/?$ categories.html?slug=$1&view=quotes&lang=en [L,QSA]

# ==========================================
# 来源相关页面
# ==========================================

# 来源列表页面
RewriteRule ^sources/?$ sources.html [L]
RewriteRule ^en/sources/?$ sources.html?lang=en [L,QSA]

# 来源详情页面 /sources/republic/
RewriteRule ^sources/([^/]+)/?$ sources.html?slug=$1 [L,QSA]
RewriteRule ^en/sources/([^/]+)/?$ sources.html?slug=$1&lang=en [L,QSA]

# 来源名言列表 /sources/republic/quotes/
RewriteRule ^sources/([^/]+)/quotes/?$ sources.html?slug=$1&view=quotes [L,QSA]
RewriteRule ^en/sources/([^/]+)/quotes/?$ sources.html?slug=$1&view=quotes&lang=en [L,QSA]

# ==========================================
# 名言相关页面
# ==========================================

# 名言详情页面 /quotes/123/
RewriteRule ^quotes/([0-9]+)/?$ quotes.html?id=$1 [L,QSA]
RewriteRule ^en/quotes/([0-9]+)/?$ quotes.html?id=$1&lang=en [L,QSA]

# ==========================================
# 搜索页面
# ==========================================

# 搜索页面 /search/ 和 /search/keyword/
RewriteRule ^search/?$ search.html [L]
RewriteRule ^search/([^/]+)/?$ search.html?q=$1 [L,QSA]
RewriteRule ^en/search/?$ search.html?lang=en [L,QSA]
RewriteRule ^en/search/([^/]+)/?$ search.html?q=$1&lang=en [L,QSA]

# ==========================================
# 静态文件和特殊页面
# ==========================================

# 处理robots.txt和sitemap.xml
RewriteRule ^robots\.txt$ robots.txt [L]
RewriteRule ^sitemap\.xml$ sitemap.xml [L]

# 404错误页面
ErrorDocument 404 /404.html

# ==========================================
# 安全和性能优化
# ==========================================

# 防止访问敏感文件
RewriteRule ^\.htaccess$ - [F,L]
RewriteRule ^\.git - [F,L]

# 如果请求的不是真实文件或目录，且不匹配上述规则
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /404.html [L]
