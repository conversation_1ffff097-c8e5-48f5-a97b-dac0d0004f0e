/**
 * API客户端
 * 负责与后端API通信
 */
class ApiClient {
    /**
     * 创建API客户端实例
     * @param {string} endpoint - API端点URL
     * @param {boolean} useMockData - 是否使用模拟数据
     */
    constructor(endpoint = 'https://api.quotese.com/graphql', useMockData = false) {
        this.endpoint = endpoint;
        this.useMockData = useMockData;
        this.mockData = {
            quotes: [
                {
                    id: 1,
                    content: "生活就像一盒巧克力，你永远不知道下一颗是什么味道。",
                    author: { id: 1, name: "福雷斯特·甘" },
                    categories: [{ id: 1, name: "电影" }, { id: 2, name: "励志" }],
                    sources: [{ id: 1, name: "阿甘正传" }]
                },
                {
                    id: 2,
                    content: "要么忙于生存，要么赶着去死。",
                    author: { id: 2, name: "安迪·杜佛雷斯" },
                    categories: [{ id: 1, name: "电影" }, { id: 3, name: "人生" }],
                    sources: [{ id: 2, name: "肖申克的救赎" }]
                },
                {
                    id: 3,
                    content: "希望是好事，也许是最好的，好事是不会消亡的。",
                    author: { id: 2, name: "安迪·杜佛雷斯" },
                    categories: [{ id: 1, name: "电影" }, { id: 4, name: "希望" }],
                    sources: [{ id: 2, name: "肖申克的救赎" }]
                }
            ],
            authors: [
                { id: 1, name: "福雷斯特·甘", quotes_count: 1 },
                { id: 2, name: "安迪·杜佛雷斯", quotes_count: 2 }
            ],
            categories: [
                { id: 1, name: "电影", quotes_count: 3 },
                { id: 2, name: "励志", quotes_count: 1 },
                { id: 3, name: "人生", quotes_count: 1 },
                { id: 4, name: "希望", quotes_count: 1 }
            ],
            sources: [
                { id: 1, name: "阿甘正传", quotes_count: 1 },
                { id: 2, name: "肖申克的救赎", quotes_count: 2 }
            ]
        };
    }

    /**
     * 获取名言列表
     * @param {number} limit - 结果数量限制
     * @param {number} offset - 结果偏移量
     * @returns {Promise<Array>} - 名言列表
     */
    async getQuotes(limit = 10, offset = 0) {
        if (this.useMockData) {
            return Promise.resolve(this.mockData.quotes.slice(offset, offset + limit));
        }
        
        try {
            const query = `
                query {
                    quotes(first: ${limit}, skip: ${offset}) {
                        id
                        content
                        author {
                            id
                            name
                        }
                        categories {
                            id
                            name
                        }
                        sources {
                            id
                            name
                        }
                    }
                }
            `;
            
            const response = await this._graphqlRequest(query);
            return response.data.quotes;
        } catch (error) {
            console.error('Error fetching quotes:', error);
            throw error;
        }
    }

    /**
     * 获取名言详情
     * @param {number} id - 名言ID
     * @returns {Promise<Object>} - 名言详情
     */
    async getQuote(id) {
        if (this.useMockData) {
            const quote = this.mockData.quotes.find(q => q.id == id);
            return Promise.resolve(quote || null);
        }
        
        try {
           