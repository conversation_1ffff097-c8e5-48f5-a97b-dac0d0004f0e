#!/bin/bash

# 紧急修复404错误 - 2025-01-16
# 修复新URL重写规则指向不存在的HTML文件的问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 项目根目录
PROJECT_ROOT="/var/www/quotese"
BACKUP_DIR="/var/www/quotese/backups/hotfix_$(date +%Y%m%d_%H%M%S)"
UPDATE_DIR="$(dirname "$0")"

echo -e "${RED}=== QuoteSe 404错误紧急修复 ===${NC}"
echo -e "${YELLOW}问题: 新URL重写规则指向不存在的HTML文件${NC}"
echo -e "${YELLOW}修复: 更新.htaccess使用现有的HTML文件名${NC}"
echo ""

# 检查权限
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}错误: 请使用sudo运行此脚本${NC}"
    exit 1
fi

# 检查项目目录
if [ ! -d "$PROJECT_ROOT" ]; then
    echo -e "${RED}错误: 项目目录不存在: $PROJECT_ROOT${NC}"
    exit 1
fi

# 创建备份目录
echo -e "${YELLOW}创建备份目录...${NC}"
mkdir -p "$BACKUP_DIR"

# 备份当前.htaccess
echo -e "${YELLOW}备份当前.htaccess文件...${NC}"
if [ -f "$PROJECT_ROOT/frontend/.htaccess" ]; then
    cp "$PROJECT_ROOT/frontend/.htaccess" "$BACKUP_DIR/.htaccess.backup"
    echo -e "${GREEN}备份完成: $BACKUP_DIR/.htaccess.backup${NC}"
else
    echo -e "${RED}警告: .htaccess文件不存在${NC}"
fi

# 检查现有HTML文件
echo -e "${YELLOW}检查现有HTML文件...${NC}"
check_files() {
    local files=(
        "frontend/author.html"
        "frontend/category.html"
        "frontend/source.html"
        "frontend/quote.html"
        "frontend/search.html"
    )
    
    local missing_files=()
    
    for file in "${files[@]}"; do
        if [ -f "$PROJECT_ROOT/$file" ]; then
            echo -e "${GREEN}  ✓ $file 存在${NC}"
        else
            missing_files+=("$file")
            echo -e "${RED}  ✗ $file 不存在${NC}"
        fi
    done
    
    if [ ${#missing_files[@]} -gt 0 ]; then
        echo -e "${RED}错误: 以下必需文件缺失，无法继续修复:${NC}"
        for file in "${missing_files[@]}"; do
            echo -e "${RED}  - $file${NC}"
        done
        return 1
    fi
    
    return 0
}

if ! check_files; then
    echo -e "${RED}修复失败: 缺少必需的HTML文件${NC}"
    exit 1
fi

# 部署修复后的.htaccess
echo -e "${YELLOW}部署修复后的.htaccess文件...${NC}"
cp "$UPDATE_DIR/frontend/.htaccess" "$PROJECT_ROOT/frontend/"

echo -e "${GREEN}.htaccess文件已更新${NC}"

# 验证Apache配置
echo -e "${YELLOW}验证Apache配置...${NC}"
if command -v apache2ctl >/dev/null 2>&1; then
    if apache2ctl configtest; then
        echo -e "${GREEN}Apache配置验证通过${NC}"
    else
        echo -e "${RED}Apache配置验证失败，恢复备份...${NC}"
        cp "$BACKUP_DIR/.htaccess.backup" "$PROJECT_ROOT/frontend/.htaccess"
        exit 1
    fi
elif command -v httpd >/dev/null 2>&1; then
    if httpd -t; then
        echo -e "${GREEN}Apache配置验证通过${NC}"
    else
        echo -e "${RED}Apache配置验证失败，恢复备份...${NC}"
        cp "$BACKUP_DIR/.htaccess.backup" "$PROJECT_ROOT/frontend/.htaccess"
        exit 1
    fi
else
    echo -e "${YELLOW}警告: 无法找到Apache配置测试命令，跳过验证${NC}"
fi

# 重新加载Apache配置
echo -e "${YELLOW}重新加载Apache配置...${NC}"
if command -v systemctl >/dev/null 2>&1; then
    if systemctl is-active --quiet apache2; then
        systemctl reload apache2
        echo -e "${GREEN}Apache2配置已重新加载${NC}"
    elif systemctl is-active --quiet httpd; then
        systemctl reload httpd
        echo -e "${GREEN}HTTPD配置已重新加载${NC}"
    else
        echo -e "${YELLOW}警告: Apache服务未运行或无法检测${NC}"
    fi
elif command -v service >/dev/null 2>&1; then
    service apache2 reload 2>/dev/null || service httpd reload 2>/dev/null || echo -e "${YELLOW}警告: 无法重新加载Apache配置${NC}"
else
    echo -e "${YELLOW}警告: 无法重新加载Apache配置，请手动重启Apache${NC}"
fi

# 测试修复后的URL
echo -e "${YELLOW}测试修复后的URL...${NC}"
test_urls() {
    local urls=(
        "http://localhost/categories/knowledge/"
        "http://localhost/authors/c-joybell-c/"
        "http://localhost/sources/alice-in-wonderland/"
    )
    
    for url in "${urls[@]}"; do
        echo -n "测试 $url ... "
        if curl -s -o /dev/null -w "%{http_code}" "$url" | grep -q "200"; then
            echo -e "${GREEN}✓ 200 OK${NC}"
        else
            local status=$(curl -s -o /dev/null -w "%{http_code}" "$url")
            echo -e "${RED}✗ HTTP $status${NC}"
        fi
    done
}

if command -v curl >/dev/null 2>&1; then
    test_urls
else
    echo -e "${YELLOW}警告: curl命令不可用，跳过URL测试${NC}"
fi

# 生成修复报告
echo -e "${YELLOW}生成修复报告...${NC}"
cat > "$BACKUP_DIR/hotfix_report.txt" << EOF
404错误紧急修复报告
==================

修复时间: $(date)
备份目录: $BACKUP_DIR
问题描述: 新URL重写规则指向不存在的HTML文件

修复内容:
- 将 authors.html → author.html
- 将 categories.html → category.html  
- 将 sources.html → source.html

受影响的URL:
- /categories/knowledge/ → category.html?slug=knowledge
- /authors/c-joybell-c/ → author.html?slug=c-joybell-c
- /sources/alice-in-wonderland/ → source.html?slug=alice-in-wonderland

修复方法:
更新.htaccess文件中的重写规则，使用现有的HTML文件名

验证步骤:
1. 访问 https://quotese.com/categories/knowledge/
2. 访问 https://quotese.com/authors/c-joybell-c/
3. 访问 https://quotese.com/sources/alice-in-wonderland/

回滚方法:
如需回滚，请执行:
sudo cp $BACKUP_DIR/.htaccess.backup $PROJECT_ROOT/frontend/.htaccess
sudo systemctl reload apache2
EOF

echo -e "${GREEN}修复报告已生成: $BACKUP_DIR/hotfix_report.txt${NC}"

# 完成
echo ""
echo -e "${GREEN}=== 紧急修复完成 ===${NC}"
echo -e "${GREEN}✓ .htaccess文件已修复${NC}"
echo -e "${GREEN}✓ Apache配置已重新加载${NC}"
echo -e "${GREEN}✓ 修复报告已生成${NC}"
echo ""
echo -e "${BLUE}请立即验证以下URL:${NC}"
echo -e "${BLUE}1. https://quotese.com/categories/knowledge/${NC}"
echo -e "${BLUE}2. https://quotese.com/authors/c-joybell-c/${NC}"
echo -e "${BLUE}3. https://quotese.com/sources/alice-in-wonderland/${NC}"
echo ""
echo -e "${YELLOW}如果问题仍然存在，请检查:${NC}"
echo -e "${YELLOW}1. Apache mod_rewrite模块是否启用${NC}"
echo -e "${YELLOW}2. .htaccess文件权限是否正确${NC}"
echo -e "${YELLOW}3. 虚拟主机配置是否允许.htaccess覆盖${NC}"
echo ""
echo -e "${GREEN}修复成功！${NC}"
