/**
 * SEO优化的URL处理器 - 2025-01-16
 * 支持RESTful风格URL、多语言、语义化路径
 */

const UrlHandler = {
    
    /**
     * 当前语言设置
     */
    currentLanguage: 'zh', // 默认中文
    
    /**
     * 支持的语言列表
     */
    supportedLanguages: ['zh', 'en'],
    
    /**
     * 语言映射
     */
    languageMap: {
        'zh': { name: '中文', code: 'zh-CN' },
        'en': { name: 'English', code: 'en-US' }
    },

    /**
     * 初始化URL处理器
     */
    init() {
        this.detectLanguage();
        this.parseCurrentUrl();
        this.setupLanguageSwitcher();
    },

    /**
     * 检测当前语言
     */
    detectLanguage() {
        const urlParams = new URLSearchParams(window.location.search);
        const langFromUrl = urlParams.get('lang');
        const pathLang = this.getLanguageFromPath();
        
        if (langFromUrl && this.supportedLanguages.includes(langFromUrl)) {
            this.currentLanguage = langFromUrl;
        } else if (pathLang) {
            this.currentLanguage = pathLang;
        } else {
            // 从浏览器语言检测
            const browserLang = navigator.language.toLowerCase();
            if (browserLang.startsWith('en')) {
                this.currentLanguage = 'en';
            } else {
                this.currentLanguage = 'zh';
            }
        }
    },

    /**
     * 从路径中获取语言
     */
    getLanguageFromPath() {
        const path = window.location.pathname;
        const match = path.match(/^\/([a-z]{2})\//);
        return match ? match[1] : null;
    },

    /**
     * 解析当前URL
     */
    parseCurrentUrl() {
        const path = window.location.pathname;
        const search = window.location.search;
        const urlParams = new URLSearchParams(search);
        
        // 移除语言前缀
        const cleanPath = path.replace(/^\/[a-z]{2}\//, '/');
        
        return {
            path: cleanPath,
            params: urlParams,
            language: this.currentLanguage,
            originalPath: path
        };
    },

    /**
     * 生成slug（URL友好的字符串）
     * @param {string} text - 原始文本
     * @returns {string} - slug
     */
    slugify(text) {
        if (!text) return '';
        
        return text
            .toString()
            .toLowerCase()
            .trim()
            // 替换中文字符为拼音（简化处理）
            .replace(/[\u4e00-\u9fff]/g, (char) => {
                // 这里可以集成拼音库，暂时使用简化处理
                const pinyinMap = {
                    '孔子': 'confucius',
                    '老子': 'laozi', 
                    '庄子': 'zhuangzi',
                    '哲学': 'philosophy',
                    '智慧': 'wisdom',
                    '人生': 'life',
                    '爱情': 'love',
                    '友谊': 'friendship'
                };
                return pinyinMap[char] || char;
            })
            // 替换空格和特殊字符为连字符
            .replace(/\s+/g, '-')
            .replace(/[^\w\-]+/g, '')
            .replace(/\-\-+/g, '-')
            .replace(/^-+/, '')
            .replace(/-+$/, '');
    },

    /**
     * 获取基础路径（包含语言前缀）
     * @returns {string} - 基础路径
     */
    getBasePath() {
        return this.currentLanguage === 'zh' ? '' : `/${this.currentLanguage}`;
    },

    /**
     * 生成作者页面URL
     * @param {Object} author - 作者对象
     * @param {string} view - 视图类型 ('detail' | 'quotes')
     * @returns {string} - 作者页面URL
     */
    getAuthorUrl(author, view = 'detail') {
        const slug = this.slugify(author.name) || `author-${author.id}`;
        const basePath = this.getBasePath();
        
        if (view === 'quotes') {
            return `${basePath}/authors/${slug}/quotes/`;
        }
        return `${basePath}/authors/${slug}/`;
    },

    /**
     * 生成类别页面URL
     * @param {Object} category - 类别对象
     * @param {string} view - 视图类型 ('detail' | 'quotes')
     * @returns {string} - 类别页面URL
     */
    getCategoryUrl(category, view = 'detail') {
        const slug = this.slugify(category.name) || `category-${category.id}`;
        const basePath = this.getBasePath();
        
        if (view === 'quotes') {
            return `${basePath}/categories/${slug}/quotes/`;
        }
        return `${basePath}/categories/${slug}/`;
    },

    /**
     * 生成来源页面URL
     * @param {Object} source - 来源对象
     * @param {string} view - 视图类型 ('detail' | 'quotes')
     * @returns {string} - 来源页面URL
     */
    getSourceUrl(source, view = 'detail') {
        const slug = this.slugify(source.name) || `source-${source.id}`;
        const basePath = this.getBasePath();
        
        if (view === 'quotes') {
            return `${basePath}/sources/${slug}/quotes/`;
        }
        return `${basePath}/sources/${slug}/`;
    },

    /**
     * 生成名言详情页面URL
     * @param {Object} quote - 名言对象
     * @returns {string} - 名言详情页面URL
     */
    getQuoteUrl(quote) {
        const basePath = this.getBasePath();
        return `${basePath}/quotes/${quote.id}/`;
    },

    /**
     * 生成搜索页面URL
     * @param {string} query - 搜索关键词
     * @returns {string} - 搜索页面URL
     */
    getSearchUrl(query) {
        const basePath = this.getBasePath();
        if (query) {
            const encodedQuery = encodeURIComponent(query);
            return `${basePath}/search/${encodedQuery}/`;
        }
        return `${basePath}/search/`;
    },

    /**
     * 生成列表页面URL
     * @param {string} type - 页面类型 ('authors' | 'categories' | 'sources')
     * @returns {string} - 列表页面URL
     */
    getListUrl(type) {
        const basePath = this.getBasePath();
        return `${basePath}/${type}/`;
    },

    /**
     * 生成首页URL
     * @returns {string} - 首页URL
     */
    getHomeUrl() {
        const basePath = this.getBasePath();
        return basePath || '/';
    },

    /**
     * 切换语言
     * @param {string} language - 目标语言
     */
    switchLanguage(language) {
        if (!this.supportedLanguages.includes(language)) {
            console.warn(`不支持的语言: ${language}`);
            return;
        }

        const currentUrl = this.parseCurrentUrl();
        let newPath = currentUrl.path;
        
        // 构建新的URL
        if (language === 'zh') {
            // 中文版本，移除语言前缀
            newPath = currentUrl.path;
        } else {
            // 其他语言，添加语言前缀
            newPath = `/${language}${currentUrl.path}`;
        }
        
        // 保持查询参数，但移除lang参数
        const params = new URLSearchParams(currentUrl.params);
        params.delete('lang');
        
        const queryString = params.toString();
        const finalUrl = newPath + (queryString ? `?${queryString}` : '');
        
        window.location.href = finalUrl;
    },

    /**
     * 更新查询参数
     * @param {string} key - 参数名
     * @param {string} value - 参数值
     */
    updateQueryParam(key, value) {
        const url = new URL(window.location);
        if (value) {
            url.searchParams.set(key, value);
        } else {
            url.searchParams.delete(key);
        }
        window.history.replaceState({}, '', url);
    },

    /**
     * 获取查询参数
     * @param {string} key - 参数名
     * @returns {string|null} - 参数值
     */
    getQueryParam(key) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(key);
    },

    /**
     * 设置语言切换器
     */
    setupLanguageSwitcher() {
        // 这个方法将在语言切换组件中调用
        document.addEventListener('DOMContentLoaded', () => {
            const languageSwitchers = document.querySelectorAll('[data-language-switcher]');
            languageSwitchers.forEach(switcher => {
                switcher.addEventListener('change', (e) => {
                    this.switchLanguage(e.target.value);
                });
            });
        });
    },

    /**
     * 生成面包屑导航数据
     * @returns {Array} - 面包屑数组
     */
    generateBreadcrumbs() {
        const currentUrl = this.parseCurrentUrl();
        const path = currentUrl.path;
        const breadcrumbs = [];
        
        // 首页
        breadcrumbs.push({
            name: this.currentLanguage === 'en' ? 'Home' : '首页',
            url: this.getHomeUrl()
        });
        
        // 根据路径生成面包屑
        if (path.startsWith('/authors/')) {
            breadcrumbs.push({
                name: this.currentLanguage === 'en' ? 'Authors' : '作者',
                url: this.getListUrl('authors')
            });
            
            const authorMatch = path.match(/\/authors\/([^\/]+)/);
            if (authorMatch && authorMatch[1]) {
                const slug = authorMatch[1];
                breadcrumbs.push({
                    name: slug.replace(/-/g, ' '),
                    url: `${this.getBasePath()}/authors/${slug}/`
                });
            }
        } else if (path.startsWith('/categories/')) {
            breadcrumbs.push({
                name: this.currentLanguage === 'en' ? 'Categories' : '分类',
                url: this.getListUrl('categories')
            });
            
            const categoryMatch = path.match(/\/categories\/([^\/]+)/);
            if (categoryMatch && categoryMatch[1]) {
                const slug = categoryMatch[1];
                breadcrumbs.push({
                    name: slug.replace(/-/g, ' '),
                    url: `${this.getBasePath()}/categories/${slug}/`
                });
            }
        } else if (path.startsWith('/sources/')) {
            breadcrumbs.push({
                name: this.currentLanguage === 'en' ? 'Sources' : '来源',
                url: this.getListUrl('sources')
            });
            
            const sourceMatch = path.match(/\/sources\/([^\/]+)/);
            if (sourceMatch && sourceMatch[1]) {
                const slug = sourceMatch[1];
                breadcrumbs.push({
                    name: slug.replace(/-/g, ' '),
                    url: `${this.getBasePath()}/sources/${slug}/`
                });
            }
        } else if (path.startsWith('/quotes/')) {
            const quoteMatch = path.match(/\/quotes\/([0-9]+)/);
            if (quoteMatch && quoteMatch[1]) {
                const id = quoteMatch[1];
                breadcrumbs.push({
                    name: this.currentLanguage === 'en' ? `Quote #${id}` : `名言 #${id}`,
                    url: `${this.getBasePath()}/quotes/${id}/`
                });
            }
        } else if (path.startsWith('/search/')) {
            breadcrumbs.push({
                name: this.currentLanguage === 'en' ? 'Search' : '搜索',
                url: this.getSearchUrl()
            });
        }
        
        return breadcrumbs;
    }
};

// 自动初始化
if (typeof window !== 'undefined') {
    UrlHandler.init();
}
