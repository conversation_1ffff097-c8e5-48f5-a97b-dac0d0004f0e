<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <link rel="stylesheet" href="css/styles.css">
    <style>
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .btn {
            background-color: #FFD300;
            border: none;
            border-radius: 4px;
            color: #333;
            cursor: pointer;
            font-weight: bold;
            padding: 8px 16px;
            margin-right: 8px;
            margin-bottom: 8px;
        }
        .btn:hover {
            background-color: #e6c000;
        }
        .result {
            background-color: #f5f5f5;
            border-radius: 4px;
            padding: 16px;
            margin-top: 16px;
            overflow: auto;
            max-height: 400px;
        }
        .error {
            color: #d32f2f;
            background-color: #ffebee;
            padding: 8px;
            border-radius: 4px;
            margin-top: 8px;
        }
        .success {
            color: #388e3c;
            background-color: #e8f5e9;
            padding: 8px;
            border-radius: 4px;
            margin-top: 8px;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            border-top-color: #FFD300;
            animation: spin 1s ease-in-out infinite;
            margin-right: 8px;
            vertical-align: middle;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
    <!-- Google Analytics -->
    <script src="js/analytics.js"></script>
</head>
<body>
    <div class="container">
        <h1>API测试页面</h1>
        <p>这个页面用于测试与后端API的连接。</p>

        <div class="card">
            <h2>API配置</h2>
            <div>
                <label for="api-endpoint">API端点：</label>
                <input type="text" id="api-endpoint" value="http://*************:8000/api/" style="width: 300px; padding: 8px; margin-right: 8px;">
                <button class="btn" id="update-endpoint">更新端点</button>
            </div>
            <div style="margin-top: 16px;">
                <label for="use-mock-data">
                    <input type="checkbox" id="use-mock-data">
                    使用模拟数据
                </label>
            </div>
        </div>

        <div class="card">
            <h2>测试API连接</h2>
            <button class="btn" id="test-connection">测试连接</button>
            <div id="connection-result" class="result" style="display: none;"></div>
        </div>

        <div class="card">
            <h2>获取名言列表</h2>
            <button class="btn" id="get-quotes">获取名言</button>
            <div id="quotes-result" class="result" style="display: none;"></div>
        </div>

        <div class="card">
            <h2>获取作者列表</h2>
            <button class="btn" id="get-authors">获取作者</button>
            <div id="authors-result" class="result" style="display: none;"></div>
        </div>

        <div class="card">
            <h2>获取类别列表</h2>
            <button class="btn" id="get-categories">获取类别</button>
            <div id="categories-result" class="result" style="display: none;"></div>
        </div>

        <div class="card">
            <h2>获取来源列表</h2>
            <button class="btn" id="get-sources">获取来源</button>
            <div id="sources-result" class="result" style="display: none;"></div>
        </div>

        <div class="card">
            <h2>搜索名言</h2>
            <input type="text" id="search-query" placeholder="输入搜索关键词" style="width: 300px; padding: 8px; margin-right: 8px;">
            <button class="btn" id="search-quotes">搜索</button>
            <div id="search-result" class="result" style="display: none;"></div>
        </div>
    </div>

    <!-- JavaScript -->
    <!-- Mock Data -->
    <script src="js/mock-data.js"></script>

    <!-- API Client -->
    <script src="js/api-client.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化UI
            const useMockDataCheckbox = document.getElementById('use-mock-data');
            useMockDataCheckbox.checked = window.ApiClient.useMockData;

            // 更新API端点
            document.getElementById('update-endpoint').addEventListener('click', function() {
                const endpoint = document.getElementById('api-endpoint').value;
                window.ApiClient = new ApiClient(endpoint, useMockDataCheckbox.checked);
                showSuccess('connection-result', `API端点已更新为：${endpoint}`);
            });

            // 切换模拟数据
            useMockDataCheckbox.addEventListener('change', function() {
                window.ApiClient = new ApiClient(
                    document.getElementById('api-endpoint').value,
                    this.checked
                );
                showSuccess('connection-result', `模拟数据已${this.checked ? '启用' : '禁用'}`);
            });

            // 测试连接
            document.getElementById('test-connection').addEventListener('click', async function() {
                const resultElement = document.getElementById('connection-result');
                resultElement.style.display = 'block';
                resultElement.innerHTML = '<div class="loading"></div> 正在测试连接...';

                try {
                    const query = `
                        query {
                            quotesCount
                        }
                    `;
                    const result = await window.ApiClient.query(query);
                    showSuccess('connection-result', '连接成功！API正常工作。');
                    resultElement.innerHTML += `<pre>${JSON.stringify(result, null, 2)}</pre>`;
                } catch (error) {
                    showError('connection-result', `连接失败：${error.message}`);
                }
            });

            // 获取名言
            document.getElementById('get-quotes').addEventListener('click', async function() {
                const resultElement = document.getElementById('quotes-result');
                resultElement.style.display = 'block';
                resultElement.innerHTML = '<div class="loading"></div> 正在获取名言...';

                try {
                    const result = await window.ApiClient.getQuotes(1, 5);
                    showSuccess('quotes-result', '获取名言成功！');
                    resultElement.innerHTML += `<pre>${JSON.stringify(result, null, 2)}</pre>`;
                } catch (error) {
                    showError('quotes-result', `获取名言失败：${error.message}`);
                }
            });

            // 获取作者
            document.getElementById('get-authors').addEventListener('click', async function() {
                const resultElement = document.getElementById('authors-result');
                resultElement.style.display = 'block';
                resultElement.innerHTML = '<div class="loading"></div> 正在获取作者...';

                try {
                    const result = await window.ApiClient.getAuthors(10);
                    showSuccess('authors-result', '获取作者成功！');
                    resultElement.innerHTML += `<pre>${JSON.stringify(result, null, 2)}</pre>`;
                } catch (error) {
                    showError('authors-result', `获取作者失败：${error.message}`);
                }
            });

            // 获取类别
            document.getElementById('get-categories').addEventListener('click', async function() {
                const resultElement = document.getElementById('categories-result');
                resultElement.style.display = 'block';
                resultElement.innerHTML = '<div class="loading"></div> 正在获取类别...';

                try {
                    const result = await window.ApiClient.getCategories(10);
                    showSuccess('categories-result', '获取类别成功！');
                    resultElement.innerHTML += `<pre>${JSON.stringify(result, null, 2)}</pre>`;
                } catch (error) {
                    showError('categories-result', `获取类别失败：${error.message}`);
                }
            });

            // 获取来源
            document.getElementById('get-sources').addEventListener('click', async function() {
                const resultElement = document.getElementById('sources-result');
                resultElement.style.display = 'block';
                resultElement.innerHTML = '<div class="loading"></div> 正在获取来源...';

                try {
                    const result = await window.ApiClient.getSources(10);
                    showSuccess('sources-result', '获取来源成功！');
                    resultElement.innerHTML += `<pre>${JSON.stringify(result, null, 2)}</pre>`;
                } catch (error) {
                    showError('sources-result', `获取来源失败：${error.message}`);
                }
            });

            // 搜索名言
            document.getElementById('search-quotes').addEventListener('click', async function() {
                const query = document.getElementById('search-query').value;
                if (!query) {
                    showError('search-result', '请输入搜索关键词');
                    return;
                }

                const resultElement = document.getElementById('search-result');
                resultElement.style.display = 'block';
                resultElement.innerHTML = '<div class="loading"></div> 正在搜索...';

                try {
                    const result = await window.ApiClient.getQuotes(1, 5, { search: query });
                    showSuccess('search-result', '搜索成功！');
                    resultElement.innerHTML += `<pre>${JSON.stringify(result, null, 2)}</pre>`;
                } catch (error) {
                    showError('search-result', `搜索失败：${error.message}`);
                }
            });

            // 辅助函数：显示成功消息
            function showSuccess(elementId, message) {
                const element = document.getElementById(elementId);
                element.innerHTML = `<div class="success">${message}</div>`;
            }

            // 辅助函数：显示错误消息
            function showError(elementId, message) {
                const element = document.getElementById(elementId);
                element.innerHTML = `<div class="error">${message}</div>`;
            }
        });
    </script>
</body>
</html>
