<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search Test - quotese.com</title>
    
    <!-- Tailwind CSS -->
    <link href="https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="css/variables.css" rel="stylesheet">
    <link href="css/styles.css" rel="stylesheet">
    <link href="css/buttons.css" rel="stylesheet">
    <link href="css/pages/search.css" rel="stylesheet">
</head>
<body class="light-mode bg-gray-50">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-center mb-8">搜索功能测试</h1>
        
        <!-- 测试搜索框 -->
        <div class="max-w-2xl mx-auto mb-8">
            <div class="search-input-container">
                <input 
                    type="text" 
                    id="test-search-input" 
                    class="search-input" 
                    placeholder="输入搜索关键词测试..."
                    value="love"
                >
                <button id="test-search-button" class="search-button">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>
        
        <!-- 测试按钮 -->
        <div class="text-center mb-8 space-x-4">
            <button id="test-api-connection" class="btn btn-primary">
                测试API连接
            </button>
            <button id="test-search-api" class="btn btn-secondary">
                测试搜索API
            </button>
            <button id="open-search-page" class="btn btn-yellow">
                打开搜索页面
            </button>
        </div>
        
        <!-- 结果显示区域 -->
        <div id="test-results" class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold mb-4">测试结果：</h3>
            <div id="test-output" class="text-sm text-gray-600">
                点击上方按钮开始测试...
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/config.js"></script>
    <script src="js/api-client.js"></script>
    <script src="js/url-handler.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const testOutput = document.getElementById('test-output');
            
            function log(message, type = 'info') {
                const timestamp = new Date().toLocaleTimeString();
                const color = type === 'error' ? 'text-red-600' : type === 'success' ? 'text-green-600' : 'text-gray-600';
                testOutput.innerHTML += `<div class="${color}">[${timestamp}] ${message}</div>`;
                testOutput.scrollTop = testOutput.scrollHeight;
            }
            
            // 测试API连接
            document.getElementById('test-api-connection').addEventListener('click', async function() {
                log('开始测试API连接...');
                
                try {
                    if (!window.ApiClient) {
                        log('错误：ApiClient未初始化', 'error');
                        return;
                    }
                    
                    log(`API端点：${window.ApiClient.apiEndpoint}`);
                    
                    // 测试获取统计信息
                    const stats = await window.ApiClient.getStats();
                    log(`API连接成功！统计信息：${JSON.stringify(stats)}`, 'success');
                    
                } catch (error) {
                    log(`API连接失败：${error.message}`, 'error');
                }
            });
            
            // 测试搜索API
            document.getElementById('test-search-api').addEventListener('click', async function() {
                const searchInput = document.getElementById('test-search-input');
                const query = searchInput.value.trim();
                
                if (!query) {
                    log('请输入搜索关键词', 'error');
                    return;
                }
                
                log(`开始搜索："${query}"...`);
                
                try {
                    const result = await window.ApiClient.getQuotes(1, 5, { search: query });
                    log(`搜索成功！找到 ${result.totalCount} 条结果，显示前 ${result.quotes.length} 条`, 'success');
                    
                    if (result.quotes.length > 0) {
                        log('示例结果：');
                        result.quotes.slice(0, 2).forEach((quote, index) => {
                            log(`${index + 1}. "${quote.content}" - ${quote.author ? quote.author.name : '未知作者'}`);
                        });
                    }
                    
                } catch (error) {
                    log(`搜索失败：${error.message}`, 'error');
                }
            });
            
            // 打开搜索页面
            document.getElementById('open-search-page').addEventListener('click', function() {
                const searchInput = document.getElementById('test-search-input');
                const query = searchInput.value.trim();
                
                if (query) {
                    const searchUrl = window.UrlHandler ? 
                        window.UrlHandler.getSearchUrl(query) : 
                        `search.html?q=${encodeURIComponent(query)}`;
                    log(`打开搜索页面：${searchUrl}`);
                    window.open(searchUrl, '_blank');
                } else {
                    log(`打开空搜索页面：search.html`);
                    window.open('search.html', '_blank');
                }
            });
            
            // 搜索框回车事件
            document.getElementById('test-search-input').addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    document.getElementById('test-search-api').click();
                }
            });
            
            // 搜索按钮点击事件
            document.getElementById('test-search-button').addEventListener('click', function() {
                document.getElementById('test-search-api').click();
            });
            
            log('测试页面已加载，可以开始测试');
        });
    </script>
</body>
</html>
