/**
 * 导航组件 - SEO优化版本 2025-01-16
 * 支持多语言、RESTful URL和无障碍访问
 */

const NavigationComponent = {
    
    /**
     * 组件名称
     */
    name: 'navigation',
    
    /**
     * 默认配置
     */
    defaultConfig: {
        showLanguageSwitcher: true,
        showSearch: true,
        showMobileMenu: true,
        enableKeyboardNavigation: true,
        className: 'main-navigation'
    },

    /**
     * 渲染导航栏
     * @param {HTMLElement} container - 容器元素
     * @param {Object} config - 配置选项
     */
    render: function(container, config = {}) {
        const finalConfig = { ...this.defaultConfig, ...config };
        const currentPath = window.location.pathname;
        const currentLanguage = UrlHandler.currentLanguage || 'zh';
        
        // 生成导航链接
        const navLinks = this.generateNavLinks(currentLanguage);
        
        // 生成HTML
        const html = this.generateHTML(navLinks, currentPath, finalConfig);
        container.innerHTML = html;
        
        // 添加事件监听
        this.addEventListeners(container, finalConfig);
        
        // 设置当前页面状态
        this.setActiveState(container, currentPath);
        
        // 初始化子组件
        this.initializeSubComponents(container, finalConfig);
    },

    /**
     * 生成导航链接
     * @param {string} language - 当前语言
     * @returns {Array} - 导航链接数组
     */
    generateNavLinks: function(language) {
        const basePath = UrlHandler.getBasePath();
        
        return [
            {
                text: I18n.t('nav.home'),
                url: basePath || '/',
                icon: 'fa-home',
                key: 'home'
            },
            {
                text: I18n.t('nav.authors'),
                url: `${basePath}/authors/`,
                icon: 'fa-users',
                key: 'authors'
            },
            {
                text: I18n.t('nav.categories'),
                url: `${basePath}/categories/`,
                icon: 'fa-tags',
                key: 'categories'
            },
            {
                text: I18n.t('nav.sources'),
                url: `${basePath}/sources/`,
                icon: 'fa-book',
                key: 'sources'
            },
            {
                text: I18n.t('nav.search'),
                url: `${basePath}/search/`,
                icon: 'fa-search',
                key: 'search'
            }
        ];
    },

    /**
     * 生成HTML
     * @param {Array} navLinks - 导航链接
     * @param {string} currentPath - 当前路径
     * @param {Object} config - 配置
     * @returns {string} - HTML字符串
     */
    generateHTML: function(navLinks, currentPath, config) {
        let html = `<nav class="${config.className}" role="navigation" aria-label="${I18n.t('a11y.main_navigation')}">`;
        
        // 跳转到主要内容的链接（无障碍）
        html += `<a href="#main-content" class="skip-link" aria-label="${I18n.t('a11y.skip_to_content')}">${I18n.t('a11y.skip_to_content')}</a>`;
        
        html += '<div class="nav-container">';
        
        // Logo/品牌
        html += '<div class="nav-brand">';
        html += `<a href="${UrlHandler.getHomeUrl()}" class="brand-link" aria-label="${I18n.t('nav.home')}">`;
        html += `<span class="brand-text">${I18n.t('site.title')}</span>`;
        html += '</a>';
        html += '</div>';
        
        // 移动端菜单按钮
        if (config.showMobileMenu) {
            html += '<button class="mobile-menu-toggle" aria-expanded="false" aria-controls="nav-menu" ';
            html += `aria-label="${I18n.t('a11y.open_menu')}" title="${I18n.t('mobile.menu')}">`;
            html += '<span class="hamburger-line"></span>';
            html += '<span class="hamburger-line"></span>';
            html += '<span class="hamburger-line"></span>';
            html += '</button>';
        }
        
        // 主导航菜单
        html += '<div class="nav-menu" id="nav-menu">';
        html += '<ul class="nav-list" role="menubar">';
        
        navLinks.forEach((link, index) => {
            const isActive = this.isLinkActive(link, currentPath);
            const itemClass = isActive ? 'nav-item active' : 'nav-item';
            
            html += `<li class="${itemClass}" role="none">`;
            html += `<a href="${link.url}" class="nav-link" role="menuitem" `;
            html += `aria-label="${link.text}" title="${link.text}" `;
            html += `${isActive ? 'aria-current="page"' : ''}`;
            html += `tabindex="${index === 0 ? '0' : '-1'}">`;
            
            if (link.icon) {
                html += `<i class="fas ${link.icon}" aria-hidden="true"></i>`;
            }
            
            html += `<span class="nav-text">${link.text}</span>`;
            html += '</a>';
            html += '</li>';
        });
        
        html += '</ul>';
        
        // 搜索框
        if (config.showSearch) {
            html += this.generateSearchHTML();
        }
        
        // 语言切换器
        if (config.showLanguageSwitcher) {
            html += '<div class="nav-language-switcher" id="nav-language-switcher"></div>';
        }
        
        html += '</div>'; // nav-menu
        html += '</div>'; // nav-container
        html += '</nav>';
        
        return html;
    },

    /**
     * 生成搜索HTML
     * @returns {string} - 搜索HTML
     */
    generateSearchHTML: function() {
        let html = '<div class="nav-search">';
        html += '<form class="search-form" role="search" aria-label="搜索表单">';
        html += `<input type="search" class="search-input" placeholder="${I18n.t('search.placeholder')}" `;
        html += `aria-label="${I18n.t('search.placeholder')}" autocomplete="off" spellcheck="false">`;
        html += `<button type="submit" class="search-button" aria-label="${I18n.t('btn.search')}" title="${I18n.t('btn.search')}">`;
        html += '<i class="fas fa-search" aria-hidden="true"></i>';
        html += '</button>';
        html += '</form>';
        html += '</div>';
        
        return html;
    },

    /**
     * 检查链接是否为当前活动状态
     * @param {Object} link - 链接对象
     * @param {string} currentPath - 当前路径
     * @returns {boolean} - 是否活动
     */
    isLinkActive: function(link, currentPath) {
        // 移除语言前缀进行比较
        const cleanCurrentPath = currentPath.replace(/^\/[a-z]{2}\//, '/');
        const cleanLinkPath = link.url.replace(/^\/[a-z]{2}\//, '/');
        
        // 首页特殊处理
        if (link.key === 'home') {
            return cleanCurrentPath === '/' || cleanCurrentPath === '/home/';
        }
        
        // 其他页面匹配
        return cleanCurrentPath.startsWith(cleanLinkPath);
    },

    /**
     * 设置活动状态
     * @param {HTMLElement} container - 容器元素
     * @param {string} currentPath - 当前路径
     */
    setActiveState: function(container, currentPath) {
        // 移除所有活动状态
        const activeItems = container.querySelectorAll('.nav-item.active');
        activeItems.forEach(item => {
            item.classList.remove('active');
            const link = item.querySelector('.nav-link');
            if (link) {
                link.removeAttribute('aria-current');
            }
        });
        
        // 设置当前活动项
        const navLinks = container.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (this.isLinkActive({ url: href, key: this.getLinkKey(href) }, currentPath)) {
                const item = link.closest('.nav-item');
                if (item) {
                    item.classList.add('active');
                    link.setAttribute('aria-current', 'page');
                }
            }
        });
    },

    /**
     * 获取链接的key
     * @param {string} href - 链接地址
     * @returns {string} - 链接key
     */
    getLinkKey: function(href) {
        const cleanHref = href.replace(/^\/[a-z]{2}\//, '/');
        
        if (cleanHref === '/' || cleanHref === '/home/') return 'home';
        if (cleanHref.startsWith('/authors/')) return 'authors';
        if (cleanHref.startsWith('/categories/')) return 'categories';
        if (cleanHref.startsWith('/sources/')) return 'sources';
        if (cleanHref.startsWith('/search/')) return 'search';
        
        return '';
    },

    /**
     * 添加事件监听
     * @param {HTMLElement} container - 容器元素
     * @param {Object} config - 配置
     */
    addEventListeners: function(container, config) {
        // 移动端菜单切换
        if (config.showMobileMenu) {
            this.addMobileMenuListeners(container);
        }
        
        // 搜索功能
        if (config.showSearch) {
            this.addSearchListeners(container);
        }
        
        // 键盘导航
        if (config.enableKeyboardNavigation) {
            this.addKeyboardListeners(container);
        }
        
        // 导航链接点击
        this.addNavigationListeners(container);
    },

    /**
     * 添加移动端菜单监听
     * @param {HTMLElement} container - 容器元素
     */
    addMobileMenuListeners: function(container) {
        const toggleButton = container.querySelector('.mobile-menu-toggle');
        const navMenu = container.querySelector('.nav-menu');
        
        if (toggleButton && navMenu) {
            toggleButton.addEventListener('click', () => {
                const isExpanded = toggleButton.getAttribute('aria-expanded') === 'true';
                this.toggleMobileMenu(toggleButton, navMenu, !isExpanded);
            });
            
            // 点击外部关闭菜单
            document.addEventListener('click', (e) => {
                if (!container.contains(e.target)) {
                    this.toggleMobileMenu(toggleButton, navMenu, false);
                }
            });
        }
    },

    /**
     * 切换移动端菜单
     * @param {HTMLElement} button - 切换按钮
     * @param {HTMLElement} menu - 菜单元素
     * @param {boolean} isOpen - 是否打开
     */
    toggleMobileMenu: function(button, menu, isOpen) {
        button.setAttribute('aria-expanded', isOpen.toString());
        button.setAttribute('aria-label', I18n.t(isOpen ? 'a11y.close_menu' : 'a11y.open_menu'));
        
        if (isOpen) {
            menu.classList.add('open');
            document.body.classList.add('nav-menu-open');
        } else {
            menu.classList.remove('open');
            document.body.classList.remove('nav-menu-open');
        }
    },

    /**
     * 添加搜索监听
     * @param {HTMLElement} container - 容器元素
     */
    addSearchListeners: function(container) {
        const searchForm = container.querySelector('.search-form');
        const searchInput = container.querySelector('.search-input');
        
        if (searchForm && searchInput) {
            searchForm.addEventListener('submit', (e) => {
                e.preventDefault();
                const query = searchInput.value.trim();
                if (query) {
                    const searchUrl = UrlHandler.getSearchUrl(query);
                    window.location.href = searchUrl;
                }
            });
        }
    },

    /**
     * 添加键盘导航监听
     * @param {HTMLElement} container - 容器元素
     */
    addKeyboardListeners: function(container) {
        const navLinks = container.querySelectorAll('.nav-link');
        
        navLinks.forEach((link, index) => {
            link.addEventListener('keydown', (e) => {
                switch (e.key) {
                    case 'ArrowRight':
                        e.preventDefault();
                        const nextIndex = (index + 1) % navLinks.length;
                        navLinks[nextIndex].focus();
                        break;
                        
                    case 'ArrowLeft':
                        e.preventDefault();
                        const prevIndex = index > 0 ? index - 1 : navLinks.length - 1;
                        navLinks[prevIndex].focus();
                        break;
                        
                    case 'Home':
                        e.preventDefault();
                        navLinks[0].focus();
                        break;
                        
                    case 'End':
                        e.preventDefault();
                        navLinks[navLinks.length - 1].focus();
                        break;
                }
            });
        });
    },

    /**
     * 添加导航监听
     * @param {HTMLElement} container - 容器元素
     */
    addNavigationListeners: function(container) {
        const navLinks = container.querySelectorAll('.nav-link');
        
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                // 可以在这里添加自定义的导航逻辑
                // 例如：单页应用的路由处理、分析跟踪等
            });
        });
    },

    /**
     * 初始化子组件
     * @param {HTMLElement} container - 容器元素
     * @param {Object} config - 配置
     */
    initializeSubComponents: function(container, config) {
        // 初始化语言切换器
        if (config.showLanguageSwitcher) {
            const languageSwitcherContainer = container.querySelector('#nav-language-switcher');
            if (languageSwitcherContainer && typeof LanguageSwitcherComponent !== 'undefined') {
                LanguageSwitcherComponent.render(languageSwitcherContainer, {
                    style: 'dropdown',
                    showFlags: true,
                    showNames: false
                });
            }
        }
    },

    /**
     * 更新导航状态
     * @param {HTMLElement} container - 容器元素
     */
    update: function(container) {
        const currentPath = window.location.pathname;
        this.setActiveState(container, currentPath);
        
        // 更新语言切换器
        const languageSwitcher = container.querySelector('#nav-language-switcher');
        if (languageSwitcher && typeof LanguageSwitcherComponent !== 'undefined') {
            LanguageSwitcherComponent.update(languageSwitcher);
        }
    }
};

// 注册组件
if (typeof ComponentRegistry !== 'undefined') {
    ComponentRegistry.register(NavigationComponent.name, NavigationComponent);
}

// 监听路由变化，更新导航状态
if (typeof document !== 'undefined') {
    document.addEventListener('routeChanged', () => {
        const navContainers = document.querySelectorAll('[data-component="navigation"]');
        navContainers.forEach(container => {
            NavigationComponent.update(container);
        });
    });
}

// 导出组件
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NavigationComponent;
}
