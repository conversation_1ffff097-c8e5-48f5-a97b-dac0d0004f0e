<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search Results | quotese.com</title>
    <meta name="description" content="Search results for quotes, authors, categories and sources from our collection.">
    <meta name="keywords" content="search quotes, find quotes, quote search">
    <link rel="canonical" href="https://quotese.com/search/">
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/responsive.css">
</head>
<body>
    <!-- 导航栏 -->
    <div id="navigation-container"></div>
    
    <!-- 面包屑导航 -->
    <div id="breadcrumb-container"></div>
    
    <main class="container">
        <h1>Search Results</h1>
        
        <div class="search-container">
            <input type="text" id="search-input" placeholder="Search for quotes, authors, categories...">
            <button id="search-button">Search</button>
        </div>
        
        <div id="search-results">
            <div class="loading" id="loading-indicator" style="display: none;">Loading...</div>
            <div id="no-results" style="display: none;">No results found for your search.</div>
            <div id="results-container"></div>
        </div>
        
        <div id="pagination-container" class="pagination"></div>
    </main>
    
    <!-- 页脚 -->
    <div id="footer-container"></div>
    
    <!-- JavaScript -->
    <script src="js/components.js"></script>
    <script src="js/api-client.js"></script>
    <script src="js/url-handler.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', async function() {
            // 加载组件
            await loadComponents();
            
            // 获取搜索查询
            const urlParams = new URLSearchParams(window.location.search);
            const query = urlParams.get('q');
            
            // 设置搜索输入框的值
            const searchInput = document.getElementById('search-input');
            if (searchInput && query) {
                searchInput.value = query;
            }
            
            // 绑定搜索按钮事件
            const searchButton = document.getElementById('search-button');
            if (searchButton && searchInput) {
                searchButton.addEventListener('click', function() {
                    const newQuery = searchInput.value.trim();
                    if (newQuery) {
                        window.location.href = `search.html?q=${encodeURIComponent(newQuery)}`;
                    }
                });
                
                searchInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        const newQuery = searchInput.value.trim();
                        if (newQuery) {
                            window.location.href = `search.html?q=${encodeURIComponent(newQuery)}`;
                        }
                    }
                });
            }
            
            // 如果有查询，执行搜索
            if (query) {
                await performSearch(query);
            }
        });
        
        // 加载组件
        async function loadComponents() {
            // 加载导航栏
            const navContainer = document.getElementById('navigation-container');
            if (navContainer && window.Navigation) {
                await window.Navigation.init(navContainer);
            }
            
            // 加载面包屑导航
            const breadcrumbContainer = document.getElementById('breadcrumb-container');
            if (breadcrumbContainer && window.Breadcrumb) {
                window.Breadcrumb.init(breadcrumbContainer);
            }
            
            // 加载页脚
            const footerContainer = document.getElementById('footer-container');
            if (footerContainer && window.Footer) {
                window.Footer.init(footerContainer);
            }
        }
        
        // 执行搜索
        async function performSearch(query) {
            const loadingIndicator = document.getElementById('loading-indicator');
            const noResults = document.getElementById('no-results');
            const resultsContainer = document.getElementById('results-container');
            
            // 显示加载指示器
            if (loadingIndicator) loadingIndicator.style.display = 'block';
            if (noResults) noResults.style.display = 'none';
            if (resultsContainer) resultsContainer.innerHTML = '';
            
            try {
                // 使用API客户端搜索
                const apiClient = new ApiClient(window.AppConfig.apiEndpoint, window.AppConfig.useMockData);
                const results = await apiClient.searchQuotes(query);
                
                // 隐藏加载指示器
                if (loadingIndicator) loadingIndicator.style.display = 'none';
                
                // 处理结果
                if (results && results.length > 0) {
                    // 显示结果
                    displaySearchResults(results);
                } else {
                    // 显示无结果消息
                    if (noResults) noResults.style.display = 'block';
                }
            } catch (error) {
                console.error('Search error:', error);
                // 隐藏加载指示器
                if (loadingIndicator) loadingIndicator.style.display = 'none';
                // 显示错误消息
                if (resultsContainer) {
                    resultsContainer.innerHTML = `<div class="error">Error performing search: ${error.message}</div>`;
                }
            }
        }
        
        // 显示搜索结果
        function displaySearchResults(quotes) {
            const resultsContainer = document.getElementById('results-container');
            if (!resultsContainer) return;
            
            resultsContainer.innerHTML = '';
            
            quotes.forEach(quote => {
                const quoteElement = document.createElement('div');
                quoteElement.className = 'quote-card';
                
                // 构建作者链接
                let authorLink = '';
                if (quote.author) {
                    authorLink = `<a href="author.html?id=${quote.author.id}&name=${encodeURIComponent(quote.author.name)}">${quote.author.name}</a>`;
                }
                
                // 构建类别链接
                let categoriesLinks = '';
                if (quote.categories && quote.categories.length > 0) {
                    const categoryLinks = quote.categories.map(category => 
                        `<a href="category.html?id=${category.id}&name=${encodeURIComponent(category.name)}">${category.name}</a>`
                    );
                    categoriesLinks = `<div class="categories">Categories: ${categoryLinks.join(', ')}</div>`;
                }
                
                quoteElement.innerHTML = `
                    <div class="quote-content">
                        <a href="quote.html?id=${quote.id}">${quote.content}</a>
                    </div>
                    <div class="quote-author">— ${authorLink}</div>
                    ${categoriesLinks}
                `;
                
                resultsContainer.appendChild(quoteElement);
            });
        }
    </script>
</body>
</html>
<!-- 
    2023-05-10: 新增文件
    功能说明: 创建了搜索结果页面，实现了搜索功能和结果展示
    包含以下功能:
    1. 搜索表单，支持输入查询并提交
    2. 搜索结果展示，包括名言内容、作者和类别
    3. 加载状态和无结果提示
    4. 与API客户端集成，调用searchQuotes方法获取搜索结果
-->