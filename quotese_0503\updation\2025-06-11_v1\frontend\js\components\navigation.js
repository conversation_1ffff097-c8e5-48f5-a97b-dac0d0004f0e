/**
 * 导航组件
 * 负责网站顶部导航栏的初始化和交互
 */
const Navigation = {
    /**
     * 初始化导航组件
     * @param {HTMLElement} container - 导航容器元素
     * @returns {Promise} - 初始化完成的Promise
     */
    async init(container) {
        if (!container) return Promise.reject('Navigation container not found');
        
        try {
            // 加载导航HTML
            const response = await fetch('components/navigation.html');
            const html = await response.text();
            
            // 插入HTML
            container.innerHTML = html;
            
            // 初始化导航交互
            this._initNavigation(container);
            
            return Promise.resolve();
        } catch (error) {
            console.error('Error initializing navigation:', error);
            return Promise.reject(error);
        }
    },
    
    /**
     * 初始化导航交互
     * @param {HTMLElement} container - 导航容器元素
     * @private
     */
    _initNavigation(container) {
        const navElement = container.querySelector('nav');
        if (!navElement) return;
        
        // 移动端菜单切换
        const menuToggle = navElement.querySelector('.menu-toggle');
        const mobileMenu = navElement.querySelector('.mobile-menu');
        
        if (menuToggle && mobileMenu) {
            menuToggle.addEventListener('click', function() {
                mobileMenu.classList.toggle('active');
                menuToggle.setAttribute('aria-expanded', 
                    mobileMenu.classList.contains('active') ? 'true' : 'false');
            });
        }
        
        // 高亮当前页面对应的导航项
        this._highlightCurrentPage(navElement);
        
        // 2025-06-11: 修改部分开始
        // 功能说明: 更新了搜索功能的事件处理逻辑
        // 搜索功能
        const searchInput = navElement.querySelector('.search-input');
        const searchButton = navElement.querySelector('.search-button');

        if (searchInput && searchButton) {
            // 点击搜索按钮时提交搜索
            searchButton.addEventListener('click', handleSearch);
            
            // 在搜索输入框中按下回车键时提交搜索
            searchInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    handleSearch();
                }
            });
        }

        // 提交搜索
        function handleSearch() {
            const query = searchInput.value.trim();
            if (query) {
                window.location.href = `search.html?q=${encodeURIComponent(query)}`;
            }
        }
        // 2025-06-11: 修改部分结束
    },
    
    /**
     * 高亮当前页面对应的导航项
     * @param {HTMLElement} navElement - 导航元素
     * @private
     */
    _highlightCurrentPage(navElement) {
        const currentPath = window.location.pathname;
        const navLinks = navElement.querySelectorAll('a');
        
        navLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (!href) return;
            
            // 移除所有active类
            link.classList.remove('active');
            
            // 检查当前路径是否匹配链接
            if (currentPath === href || 
                currentPath.startsWith(href) && href !== '/' ||
                (currentPath === '/' && href === 'index.html')) {
                link.classList.add('active');
            }
        });
    }
};

// 如果在浏览器环境中，将Navigation添加到window对象
if (typeof window !== 'undefined') {
    window.Navigation = Navigation;
}

// 如果支持模块导出，导出Navigation
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Navigation;
}