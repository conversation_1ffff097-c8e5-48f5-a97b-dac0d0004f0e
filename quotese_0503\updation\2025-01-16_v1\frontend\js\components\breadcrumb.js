/**
 * 面包屑导航组件 - 2025-01-16
 * 提供SEO友好的导航路径和结构化数据
 */

const BreadcrumbComponent = {
    
    /**
     * 组件名称
     */
    name: 'breadcrumb',
    
    /**
     * 默认配置
     */
    defaultConfig: {
        showHome: true,
        separator: '/',
        maxItems: 5,
        showStructuredData: true,
        className: 'breadcrumb-nav'
    },
    
    /**
     * 渲染面包屑导航
     * @param {HTMLElement} container - 容器元素
     * @param {Object} config - 配置选项
     */
    render: function(container, config = {}) {
        const finalConfig = { ...this.defaultConfig, ...config };
        const breadcrumbs = this.generateBreadcrumbs();
        
        if (!breadcrumbs || breadcrumbs.length === 0) {
            container.innerHTML = '';
            return;
        }
        
        // 生成HTML
        const html = this.generateHTML(breadcrumbs, finalConfig);
        container.innerHTML = html;
        
        // 添加结构化数据
        if (finalConfig.showStructuredData) {
            this.addStructuredData(breadcrumbs);
        }
        
        // 添加事件监听
        this.addEventListeners(container);
    },
    
    /**
     * 生成面包屑数据
     * @returns {Array} - 面包屑数组
     */
    generateBreadcrumbs: function() {
        const currentUrl = UrlHandler.parseCurrentUrl();
        const path = currentUrl.path;
        const params = currentUrl.params;
        const breadcrumbs = [];
        
        // 首页
        breadcrumbs.push({
            name: I18n.t('breadcrumb.home'),
            url: UrlHandler.getHomeUrl(),
            position: 1
        });
        
        // 根据路径生成面包屑
        if (path.startsWith('/authors')) {
            this.addAuthorBreadcrumbs(breadcrumbs, path, params);
        } else if (path.startsWith('/categories')) {
            this.addCategoryBreadcrumbs(breadcrumbs, path, params);
        } else if (path.startsWith('/sources')) {
            this.addSourceBreadcrumbs(breadcrumbs, path, params);
        } else if (path.startsWith('/quotes')) {
            this.addQuoteBreadcrumbs(breadcrumbs, path, params);
        } else if (path.startsWith('/search')) {
            this.addSearchBreadcrumbs(breadcrumbs, path, params);
        }
        
        return breadcrumbs;
    },
    
    /**
     * 添加作者相关面包屑
     */
    addAuthorBreadcrumbs: function(breadcrumbs, path, params) {
        // 作者列表
        breadcrumbs.push({
            name: I18n.t('breadcrumb.authors'),
            url: UrlHandler.getListUrl('authors'),
            position: breadcrumbs.length + 1
        });
        
        // 作者详情
        const authorMatch = path.match(/\/authors\/([^\/]+)/);
        if (authorMatch) {
            const slug = authorMatch[1];
            const authorName = this.formatSlugToName(slug);
            
            breadcrumbs.push({
                name: authorName,
                url: `${UrlHandler.getBasePath()}/authors/${slug}/`,
                position: breadcrumbs.length + 1
            });
            
            // 作者名言列表
            if (path.includes('/quotes')) {
                breadcrumbs.push({
                    name: I18n.t('breadcrumb.quotes'),
                    url: `${UrlHandler.getBasePath()}/authors/${slug}/quotes/`,
                    position: breadcrumbs.length + 1
                });
            }
        }
    },
    
    /**
     * 添加类别相关面包屑
     */
    addCategoryBreadcrumbs: function(breadcrumbs, path, params) {
        // 类别列表
        breadcrumbs.push({
            name: I18n.t('breadcrumb.categories'),
            url: UrlHandler.getListUrl('categories'),
            position: breadcrumbs.length + 1
        });
        
        // 类别详情
        const categoryMatch = path.match(/\/categories\/([^\/]+)/);
        if (categoryMatch) {
            const slug = categoryMatch[1];
            const categoryName = this.formatSlugToName(slug);
            
            breadcrumbs.push({
                name: categoryName,
                url: `${UrlHandler.getBasePath()}/categories/${slug}/`,
                position: breadcrumbs.length + 1
            });
            
            // 类别名言列表
            if (path.includes('/quotes')) {
                breadcrumbs.push({
                    name: I18n.t('breadcrumb.quotes'),
                    url: `${UrlHandler.getBasePath()}/categories/${slug}/quotes/`,
                    position: breadcrumbs.length + 1
                });
            }
        }
    },
    
    /**
     * 添加来源相关面包屑
     */
    addSourceBreadcrumbs: function(breadcrumbs, path, params) {
        // 来源列表
        breadcrumbs.push({
            name: I18n.t('breadcrumb.sources'),
            url: UrlHandler.getListUrl('sources'),
            position: breadcrumbs.length + 1
        });
        
        // 来源详情
        const sourceMatch = path.match(/\/sources\/([^\/]+)/);
        if (sourceMatch) {
            const slug = sourceMatch[1];
            const sourceName = this.formatSlugToName(slug);
            
            breadcrumbs.push({
                name: sourceName,
                url: `${UrlHandler.getBasePath()}/sources/${slug}/`,
                position: breadcrumbs.length + 1
            });
            
            // 来源名言列表
            if (path.includes('/quotes')) {
                breadcrumbs.push({
                    name: I18n.t('breadcrumb.quotes'),
                    url: `${UrlHandler.getBasePath()}/sources/${slug}/quotes/`,
                    position: breadcrumbs.length + 1
                });
            }
        }
    },
    
    /**
     * 添加名言相关面包屑
     */
    addQuoteBreadcrumbs: function(breadcrumbs, path, params) {
        const quoteMatch = path.match(/\/quotes\/([0-9]+)/);
        if (quoteMatch) {
            const quoteId = quoteMatch[1];
            breadcrumbs.push({
                name: I18n.t('breadcrumb.quotes'),
                url: `${UrlHandler.getBasePath()}/quotes/${quoteId}/`,
                position: breadcrumbs.length + 1
            });
        }
    },
    
    /**
     * 添加搜索相关面包屑
     */
    addSearchBreadcrumbs: function(breadcrumbs, path, params) {
        breadcrumbs.push({
            name: I18n.t('breadcrumb.search'),
            url: UrlHandler.getSearchUrl(),
            position: breadcrumbs.length + 1
        });
        
        // 搜索关键词
        const searchMatch = path.match(/\/search\/([^\/]+)/);
        if (searchMatch) {
            const query = decodeURIComponent(searchMatch[1]);
            breadcrumbs.push({
                name: `"${query}"`,
                url: UrlHandler.getSearchUrl(query),
                position: breadcrumbs.length + 1
            });
        }
    },
    
    /**
     * 格式化slug为显示名称
     * @param {string} slug - URL slug
     * @returns {string} - 格式化后的名称
     */
    formatSlugToName: function(slug) {
        return slug
            .replace(/-/g, ' ')
            .replace(/\b\w/g, l => l.toUpperCase());
    },
    
    /**
     * 生成HTML
     * @param {Array} breadcrumbs - 面包屑数据
     * @param {Object} config - 配置
     * @returns {string} - HTML字符串
     */
    generateHTML: function(breadcrumbs, config) {
        const items = breadcrumbs.slice(0, config.maxItems);
        
        let html = `<nav class="${config.className}" aria-label="${I18n.t('a11y.page_navigation')}">`;
        html += '<ol class="breadcrumb-list">';
        
        items.forEach((item, index) => {
            const isLast = index === items.length - 1;
            const itemClass = isLast ? 'breadcrumb-item current' : 'breadcrumb-item';
            
            html += `<li class="${itemClass}">`;
            
            if (isLast) {
                html += `<span class="breadcrumb-text" aria-current="page">${item.name}</span>`;
            } else {
                html += `<a href="${item.url}" class="breadcrumb-link">${item.name}</a>`;
                html += `<span class="breadcrumb-separator" aria-hidden="true">${config.separator}</span>`;
            }
            
            html += '</li>';
        });
        
        html += '</ol>';
        html += '</nav>';
        
        return html;
    },
    
    /**
     * 添加结构化数据
     * @param {Array} breadcrumbs - 面包屑数据
     */
    addStructuredData: function(breadcrumbs) {
        const structuredData = {
            "@context": "https://schema.org",
            "@type": "BreadcrumbList",
            "itemListElement": breadcrumbs.map(item => ({
                "@type": "ListItem",
                "position": item.position,
                "name": item.name,
                "item": window.location.origin + item.url
            }))
        };
        
        // 移除现有的面包屑结构化数据
        const existingScript = document.querySelector('script[type="application/ld+json"][data-breadcrumb]');
        if (existingScript) {
            existingScript.remove();
        }
        
        // 添加新的结构化数据
        const script = document.createElement('script');
        script.type = 'application/ld+json';
        script.setAttribute('data-breadcrumb', 'true');
        script.textContent = JSON.stringify(structuredData);
        document.head.appendChild(script);
    },
    
    /**
     * 添加事件监听
     * @param {HTMLElement} container - 容器元素
     */
    addEventListeners: function(container) {
        // 点击事件处理
        container.addEventListener('click', (e) => {
            const link = e.target.closest('.breadcrumb-link');
            if (link) {
                // 可以在这里添加自定义的导航逻辑
                // 例如：单页应用的路由处理
            }
        });
    },
    
    /**
     * 更新面包屑
     * @param {HTMLElement} container - 容器元素
     */
    update: function(container) {
        this.render(container);
    },
    
    /**
     * 获取当前面包屑数据
     * @returns {Array} - 面包屑数组
     */
    getCurrentBreadcrumbs: function() {
        return this.generateBreadcrumbs();
    }
};

// 注册组件
if (typeof ComponentRegistry !== 'undefined') {
    ComponentRegistry.register(BreadcrumbComponent.name, BreadcrumbComponent);
}

// 导出组件
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BreadcrumbComponent;
}
