# SEO优化的URL重写规则 - 2025-01-16 (修复版)
# 实现RESTful风格URL，去除.html扩展名，支持多语言
# 修复：使用现有的HTML文件名 (author.html, category.html, source.html)

# 启用重写引擎
RewriteEngine On

# 设置基础URL
RewriteBase /

# 强制使用HTTPS（生产环境启用）
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# ==========================================
# 向后兼容性 - 301重定向旧URL到新URL
# ==========================================

# 重定向旧的作者页面
RewriteCond %{QUERY_STRING} ^id=([0-9]+)&name=([^&]+)$
RewriteRule ^author\.html$ /authors/%2/? [R=301,L]

# 重定向旧的类别页面  
RewriteCond %{QUERY_STRING} ^id=([0-9]+)&name=([^&]+)$
RewriteRule ^category\.html$ /categories/%2/? [R=301,L]

# 重定向旧的来源页面
RewriteCond %{QUERY_STRING} ^id=([0-9]+)&name=([^&]+)$
RewriteRule ^source\.html$ /sources/%2/? [R=301,L]

# 重定向旧的名言页面
RewriteCond %{QUERY_STRING} ^id=([0-9]+)$
RewriteRule ^quote\.html$ /quotes/%1/? [R=301,L]

# 重定向旧的搜索页面
RewriteCond %{QUERY_STRING} ^q=([^&]+)$
RewriteRule ^search\.html$ /search/%1/? [R=301,L]

# ==========================================
# 多语言支持
# ==========================================

# 语言检测和重定向（可选）
# RewriteCond %{HTTP:Accept-Language} ^en [NC]
# RewriteCond %{REQUEST_URI} !^/en/
# RewriteRule ^(.*)$ /en/$1 [R=302,L]

# ==========================================
# 新的RESTful URL结构
# ==========================================

# 首页处理
RewriteRule ^$ index.html [L]
RewriteRule ^home/?$ index.html [L]

# 英文版本首页
RewriteRule ^en/?$ index.html?lang=en [L,QSA]
RewriteRule ^en/home/<USER>

# ==========================================
# 作者相关页面 - 使用现有的 author.html
# ==========================================

# 作者列表页面
RewriteRule ^authors/?$ author.html [L]
RewriteRule ^en/authors/?$ author.html?lang=en [L,QSA]

# 作者详情页面 /authors/shakespeare/
RewriteRule ^authors/([^/]+)/?$ author.html?slug=$1 [L,QSA]
RewriteRule ^en/authors/([^/]+)/?$ author.html?slug=$1&lang=en [L,QSA]

# 作者名言列表 /authors/shakespeare/quotes/
RewriteRule ^authors/([^/]+)/quotes/?$ author.html?slug=$1&view=quotes [L,QSA]
RewriteRule ^en/authors/([^/]+)/quotes/?$ author.html?slug=$1&view=quotes&lang=en [L,QSA]

# ==========================================
# 类别相关页面 - 使用现有的 category.html
# ==========================================

# 类别列表页面
RewriteRule ^categories/?$ category.html [L]
RewriteRule ^en/categories/?$ category.html?lang=en [L,QSA]

# 类别详情页面 /categories/philosophy/
RewriteRule ^categories/([^/]+)/?$ category.html?slug=$1 [L,QSA]
RewriteRule ^en/categories/([^/]+)/?$ category.html?slug=$1&lang=en [L,QSA]

# 类别名言列表 /categories/philosophy/quotes/
RewriteRule ^categories/([^/]+)/quotes/?$ category.html?slug=$1&view=quotes [L,QSA]
RewriteRule ^en/categories/([^/]+)/quotes/?$ category.html?slug=$1&view=quotes&lang=en [L,QSA]

# ==========================================
# 来源相关页面 - 使用现有的 source.html
# ==========================================

# 来源列表页面
RewriteRule ^sources/?$ source.html [L]
RewriteRule ^en/sources/?$ source.html?lang=en [L,QSA]

# 来源详情页面 /sources/republic/
RewriteRule ^sources/([^/]+)/?$ source.html?slug=$1 [L,QSA]
RewriteRule ^en/sources/([^/]+)/?$ source.html?slug=$1&lang=en [L,QSA]

# 来源名言列表 /sources/republic/quotes/
RewriteRule ^sources/([^/]+)/quotes/?$ source.html?slug=$1&view=quotes [L,QSA]
RewriteRule ^en/sources/([^/]+)/quotes/?$ source.html?slug=$1&view=quotes&lang=en [L,QSA]

# ==========================================
# 名言相关页面 - 使用现有的 quote.html
# ==========================================

# 名言详情页面 /quotes/123/
RewriteRule ^quotes/([0-9]+)/?$ quote.html?id=$1 [L,QSA]
RewriteRule ^en/quotes/([0-9]+)/?$ quote.html?id=$1&lang=en [L,QSA]

# ==========================================
# 搜索页面 - 使用现有的 search.html
# ==========================================

# 搜索页面 /search/ 和 /search/keyword/
RewriteRule ^search/?$ search.html [L]
RewriteRule ^search/([^/]+)/?$ search.html?q=$1 [L,QSA]
RewriteRule ^en/search/?$ search.html?lang=en [L,QSA]
RewriteRule ^en/search/([^/]+)/?$ search.html?q=$1&lang=en [L,QSA]

# ==========================================
# 静态文件和特殊页面
# ==========================================

# 处理robots.txt和sitemap.xml
RewriteRule ^robots\.txt$ robots.txt [L]
RewriteRule ^sitemap\.xml$ sitemap.xml [L]

# 404错误页面
ErrorDocument 404 /404.html

# ==========================================
# 安全和性能优化
# ==========================================

# 防止访问敏感文件
RewriteRule ^\.htaccess$ - [F,L]
RewriteRule ^\.git - [F,L]

# 如果请求的不是真实文件或目录，且不匹配上述规则
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /404.html [L]

# ==========================================
# 缓存控制
# ==========================================

# 设置静态资源缓存
<IfModule mod_expires.c>
    ExpiresActive On
    
    # 图片文件缓存1年
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    
    # CSS和JS文件缓存1个月
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    
    # 字体文件缓存1年
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    
    # HTML文件不缓存
    ExpiresByType text/html "access plus 0 seconds"
</IfModule>

# ==========================================
# 压缩优化
# ==========================================

# 启用Gzip压缩
<IfModule mod_deflate.c>
    # 压缩HTML、CSS、JavaScript、XML等文本文件
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE text/javascript
    AddOutputFilterByType DEFLATE application/json
    
    # 不压缩图片和其他二进制文件
    SetEnvIfNoCase Request_URI \
        \.(?:gif|jpe?g|png|ico|zip|gz|rar|bz2|pdf|txt|tar|wav|bmp|rtf|js|css)$ no-gzip dont-vary
    SetEnvIfNoCase Request_URI \.(?:exe|t?gz|zip|bz2|sit|rar)$ no-gzip dont-vary
</IfModule>

# ==========================================
# 安全头设置
# ==========================================

<IfModule mod_headers.c>
    # 防止点击劫持
    Header always append X-Frame-Options SAMEORIGIN
    
    # 防止MIME类型嗅探
    Header always set X-Content-Type-Options nosniff
    
    # XSS保护
    Header always set X-XSS-Protection "1; mode=block"
    
    # 引用策略
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # 内容安全策略（根据需要调整）
    # Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https:; style-src 'self' 'unsafe-inline' https:; img-src 'self' data: https:; font-src 'self' https:; connect-src 'self' https:;"
</IfModule>
