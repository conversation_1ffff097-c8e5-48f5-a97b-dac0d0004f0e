#!/bin/bash

# SEO优化部署脚本 - 2025-01-16
# 用于部署URL结构优化和多语言支持

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="/var/www/quotese"
BACKUP_DIR="/var/www/quotese/backups/$(date +%Y%m%d_%H%M%S)"
UPDATE_DIR="$(dirname "$0")"

echo -e "${BLUE}=== QuoteSe SEO优化部署脚本 ===${NC}"
echo -e "${BLUE}更新时间: 2025-01-16${NC}"
echo -e "${BLUE}更新内容: URL结构优化、多语言支持、SEO增强${NC}"
echo ""

# 检查权限
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}错误: 请使用sudo运行此脚本${NC}"
    exit 1
fi

# 检查项目目录
if [ ! -d "$PROJECT_ROOT" ]; then
    echo -e "${RED}错误: 项目目录不存在: $PROJECT_ROOT${NC}"
    exit 1
fi

# 创建备份目录
echo -e "${YELLOW}创建备份目录...${NC}"
mkdir -p "$BACKUP_DIR"

# 备份现有文件
echo -e "${YELLOW}备份现有文件...${NC}"
backup_files() {
    local files=(
        "frontend/.htaccess"
        "config/nginx_frontend.conf"
        "frontend/js/config.js"
        "frontend/js/url-handler.js"
        "frontend/js/components/navigation.js"
        "frontend/author.html"
        "frontend/category.html"
        "frontend/source.html"
        "frontend/quote.html"
        "frontend/search.html"
    )
    
    for file in "${files[@]}"; do
        if [ -f "$PROJECT_ROOT/$file" ]; then
            echo "  备份: $file"
            mkdir -p "$BACKUP_DIR/$(dirname "$file")"
            cp "$PROJECT_ROOT/$file" "$BACKUP_DIR/$file"
        fi
    done
}

backup_files

echo -e "${GREEN}备份完成: $BACKUP_DIR${NC}"

# 部署新文件
echo -e "${YELLOW}部署新文件...${NC}"

# 复制配置文件
echo "  部署配置文件..."
cp "$UPDATE_DIR/frontend/.htaccess" "$PROJECT_ROOT/frontend/"
cp "$UPDATE_DIR/config/nginx_frontend.conf" "$PROJECT_ROOT/config/"

# 复制JavaScript文件
echo "  部署JavaScript文件..."
cp "$UPDATE_DIR/frontend/js/config.js" "$PROJECT_ROOT/frontend/js/"
cp "$UPDATE_DIR/frontend/js/url-handler.js" "$PROJECT_ROOT/frontend/js/"

# 创建新目录结构
mkdir -p "$PROJECT_ROOT/frontend/js/i18n"
mkdir -p "$PROJECT_ROOT/frontend/js/core"
mkdir -p "$PROJECT_ROOT/frontend/js/seo"

# 复制新的JavaScript模块
cp -r "$UPDATE_DIR/frontend/js/i18n/"* "$PROJECT_ROOT/frontend/js/i18n/"
cp -r "$UPDATE_DIR/frontend/js/core/"* "$PROJECT_ROOT/frontend/js/core/"
cp -r "$UPDATE_DIR/frontend/js/seo/"* "$PROJECT_ROOT/frontend/js/seo/"

# 复制更新的组件
cp "$UPDATE_DIR/frontend/js/components/navigation.js" "$PROJECT_ROOT/frontend/js/components/"
cp "$UPDATE_DIR/frontend/js/components/breadcrumb.js" "$PROJECT_ROOT/frontend/js/components/"
cp "$UPDATE_DIR/frontend/js/components/language-switcher.js" "$PROJECT_ROOT/frontend/js/components/"

# 复制页面文件
echo "  部署页面文件..."
cp "$UPDATE_DIR/frontend/authors.html" "$PROJECT_ROOT/frontend/"

# 复制页面脚本
mkdir -p "$PROJECT_ROOT/frontend/js/pages"
cp "$UPDATE_DIR/frontend/js/pages/authors.js" "$PROJECT_ROOT/frontend/js/pages/"

echo -e "${GREEN}文件部署完成${NC}"

# 设置文件权限
echo -e "${YELLOW}设置文件权限...${NC}"
chown -R www-data:www-data "$PROJECT_ROOT/frontend"
chmod -R 644 "$PROJECT_ROOT/frontend"
chmod 755 "$PROJECT_ROOT/frontend"
find "$PROJECT_ROOT/frontend" -type d -exec chmod 755 {} \;

echo -e "${GREEN}权限设置完成${NC}"

# 验证Nginx配置
echo -e "${YELLOW}验证Nginx配置...${NC}"
if nginx -t; then
    echo -e "${GREEN}Nginx配置验证通过${NC}"
else
    echo -e "${RED}Nginx配置验证失败，恢复备份...${NC}"
    cp "$BACKUP_DIR/config/nginx_frontend.conf" "$PROJECT_ROOT/config/"
    exit 1
fi

# 重启服务
echo -e "${YELLOW}重启服务...${NC}"

# 重新加载Nginx配置
systemctl reload nginx
echo -e "${GREEN}Nginx配置已重新加载${NC}"

# 清除缓存（如果有Redis）
if systemctl is-active --quiet redis; then
    echo "清除Redis缓存..."
    redis-cli FLUSHALL
    echo -e "${GREEN}Redis缓存已清除${NC}"
fi

# 验证部署
echo -e "${YELLOW}验证部署...${NC}"

# 检查关键文件是否存在
check_files() {
    local files=(
        "frontend/.htaccess"
        "frontend/authors.html"
        "frontend/js/i18n/i18n.js"
        "frontend/js/core/router.js"
        "frontend/js/seo/meta-tags.js"
        "frontend/js/components/breadcrumb.js"
    )
    
    local missing_files=()
    
    for file in "${files[@]}"; do
        if [ ! -f "$PROJECT_ROOT/$file" ]; then
            missing_files+=("$file")
        fi
    done
    
    if [ ${#missing_files[@]} -eq 0 ]; then
        echo -e "${GREEN}所有关键文件部署成功${NC}"
        return 0
    else
        echo -e "${RED}以下文件缺失:${NC}"
        for file in "${missing_files[@]}"; do
            echo -e "${RED}  - $file${NC}"
        done
        return 1
    fi
}

if check_files; then
    echo -e "${GREEN}部署验证通过${NC}"
else
    echo -e "${RED}部署验证失败${NC}"
    exit 1
fi

# 测试URL访问
echo -e "${YELLOW}测试URL访问...${NC}"
test_urls() {
    local urls=(
        "http://localhost/"
        "http://localhost/authors/"
        "http://localhost/en/"
        "http://localhost/en/authors/"
    )
    
    for url in "${urls[@]}"; do
        if curl -s -o /dev/null -w "%{http_code}" "$url" | grep -q "200\|301\|302"; then
            echo -e "${GREEN}  ✓ $url${NC}"
        else
            echo -e "${RED}  ✗ $url${NC}"
        fi
    done
}

test_urls

# 生成部署报告
echo -e "${YELLOW}生成部署报告...${NC}"
cat > "$BACKUP_DIR/deployment_report.txt" << EOF
SEO优化部署报告
================

部署时间: $(date)
备份目录: $BACKUP_DIR
更新版本: 2025-01-16_v1

部署内容:
- URL结构优化 (RESTful风格)
- 多语言支持 (中英文)
- SEO增强 (结构化数据、meta标签)
- 面包屑导航
- 语言切换功能

部署文件:
- 配置文件: .htaccess, nginx_frontend.conf
- JavaScript模块: i18n, core, seo, components
- 页面文件: authors.html
- 页面脚本: authors.js

下一步:
1. 监控网站访问情况
2. 检查搜索引擎爬虫日志
3. 验证多语言功能
4. 测试移动端兼容性
5. 部署剩余页面文件

回滚方法:
如需回滚，请执行:
sudo cp -r $BACKUP_DIR/* $PROJECT_ROOT/
sudo systemctl reload nginx
EOF

echo -e "${GREEN}部署报告已生成: $BACKUP_DIR/deployment_report.txt${NC}"

# 完成
echo ""
echo -e "${GREEN}=== 部署完成 ===${NC}"
echo -e "${GREEN}✓ 文件备份: $BACKUP_DIR${NC}"
echo -e "${GREEN}✓ 新文件部署完成${NC}"
echo -e "${GREEN}✓ 服务重启完成${NC}"
echo -e "${GREEN}✓ 部署验证通过${NC}"
echo ""
echo -e "${BLUE}请执行以下验证步骤:${NC}"
echo -e "${BLUE}1. 访问 http://your-domain.com/ 检查首页${NC}"
echo -e "${BLUE}2. 访问 http://your-domain.com/authors/ 检查作者页面${NC}"
echo -e "${BLUE}3. 访问 http://your-domain.com/en/ 检查英文版本${NC}"
echo -e "${BLUE}4. 测试旧URL重定向功能${NC}"
echo -e "${BLUE}5. 检查移动端显示效果${NC}"
echo ""
echo -e "${YELLOW}如有问题，可使用以下命令回滚:${NC}"
echo -e "${YELLOW}sudo cp -r $BACKUP_DIR/* $PROJECT_ROOT/${NC}"
echo -e "${YELLOW}sudo systemctl reload nginx${NC}"
echo ""
echo -e "${GREEN}部署成功！${NC}"
