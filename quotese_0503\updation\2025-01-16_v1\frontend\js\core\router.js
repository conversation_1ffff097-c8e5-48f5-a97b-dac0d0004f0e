/**
 * 前端路由管理器 - 2025-01-16
 * 处理SEO友好的URL路由和页面导航
 */

const Router = {
    
    /**
     * 路由配置
     */
    routes: {
        // 首页
        '/': {
            page: 'index',
            title: 'page.home.title',
            description: 'page.home.description'
        },
        '/home/': {
            page: 'index',
            title: 'page.home.title',
            description: 'page.home.description'
        },
        
        // 作者相关
        '/authors/': {
            page: 'authors',
            view: 'list',
            title: 'page.authors.title',
            description: 'page.authors.description'
        },
        '/authors/:slug/': {
            page: 'authors',
            view: 'detail',
            title: 'page.authors.title',
            description: 'page.authors.description'
        },
        '/authors/:slug/quotes/': {
            page: 'authors',
            view: 'quotes',
            title: 'page.authors.title',
            description: 'page.authors.description'
        },
        
        // 类别相关
        '/categories/': {
            page: 'categories',
            view: 'list',
            title: 'page.categories.title',
            description: 'page.categories.description'
        },
        '/categories/:slug/': {
            page: 'categories',
            view: 'detail',
            title: 'page.categories.title',
            description: 'page.categories.description'
        },
        '/categories/:slug/quotes/': {
            page: 'categories',
            view: 'quotes',
            title: 'page.categories.title',
            description: 'page.categories.description'
        },
        
        // 来源相关
        '/sources/': {
            page: 'sources',
            view: 'list',
            title: 'page.sources.title',
            description: 'page.sources.description'
        },
        '/sources/:slug/': {
            page: 'sources',
            view: 'detail',
            title: 'page.sources.title',
            description: 'page.sources.description'
        },
        '/sources/:slug/quotes/': {
            page: 'sources',
            view: 'quotes',
            title: 'page.sources.title',
            description: 'page.sources.description'
        },
        
        // 名言详情
        '/quotes/:id/': {
            page: 'quotes',
            view: 'detail',
            title: 'page.quotes.title',
            description: 'page.quotes.description'
        },
        
        // 搜索
        '/search/': {
            page: 'search',
            view: 'form',
            title: 'page.search.title',
            description: 'page.search.description'
        },
        '/search/:query/': {
            page: 'search',
            view: 'results',
            title: 'page.search.title',
            description: 'page.search.description'
        }
    },
    
    /**
     * 当前路由信息
     */
    currentRoute: null,
    
    /**
     * 初始化路由器
     */
    init: function() {
        this.parseCurrentRoute();
        this.updatePageMeta();
        this.setupEventListeners();
    },
    
    /**
     * 解析当前路由
     */
    parseCurrentRoute: function() {
        const currentUrl = UrlHandler.parseCurrentUrl();
        const path = currentUrl.path;
        const params = currentUrl.params;
        
        // 匹配路由
        const matchedRoute = this.matchRoute(path);
        
        if (matchedRoute) {
            this.currentRoute = {
                ...matchedRoute.config,
                path: path,
                params: matchedRoute.params,
                queryParams: params,
                language: currentUrl.language
            };
        } else {
            // 404路由
            this.currentRoute = {
                page: '404',
                view: 'error',
                title: 'error.404',
                description: 'error.404.description',
                path: path,
                params: {},
                queryParams: params,
                language: currentUrl.language
            };
        }
    },
    
    /**
     * 匹配路由
     * @param {string} path - 路径
     * @returns {Object|null} - 匹配的路由配置
     */
    matchRoute: function(path) {
        // 确保路径以/结尾
        const normalizedPath = path.endsWith('/') ? path : path + '/';
        
        for (const [pattern, config] of Object.entries(this.routes)) {
            const match = this.matchPattern(normalizedPath, pattern);
            if (match) {
                return {
                    config: config,
                    params: match.params
                };
            }
        }
        
        return null;
    },
    
    /**
     * 匹配路由模式
     * @param {string} path - 实际路径
     * @param {string} pattern - 路由模式
     * @returns {Object|null} - 匹配结果
     */
    matchPattern: function(path, pattern) {
        // 将模式转换为正则表达式
        const paramNames = [];
        const regexPattern = pattern.replace(/:([^/]+)/g, (match, paramName) => {
            paramNames.push(paramName);
            return '([^/]+)';
        });
        
        const regex = new RegExp(`^${regexPattern}$`);
        const match = path.match(regex);
        
        if (match) {
            const params = {};
            paramNames.forEach((name, index) => {
                params[name] = match[index + 1];
            });
            
            return { params };
        }
        
        return null;
    },
    
    /**
     * 更新页面元数据
     */
    updatePageMeta: function() {
        if (!this.currentRoute) return;
        
        // 更新页面标题
        const title = this.generatePageTitle();
        document.title = title;
        
        // 更新meta描述
        const description = I18n.t(this.currentRoute.description);
        this.updateMetaTag('description', description);
        
        // 更新Open Graph标签
        this.updateMetaTag('og:title', title, 'property');
        this.updateMetaTag('og:description', description, 'property');
        this.updateMetaTag('og:url', window.location.href, 'property');
        
        // 更新Twitter Card标签
        this.updateMetaTag('twitter:title', title, 'name');
        this.updateMetaTag('twitter:description', description, 'name');
        
        // 更新canonical链接
        this.updateCanonicalLink();
        
        // 更新语言标签
        this.updateLanguageLinks();
    },
    
    /**
     * 生成页面标题
     * @returns {string} - 页面标题
     */
    generatePageTitle: function() {
        let title = I18n.t(this.currentRoute.title);
        const siteName = I18n.t('site.title');
        
        // 根据路由类型添加具体信息
        if (this.currentRoute.params.slug) {
            const entityName = this.formatSlugToName(this.currentRoute.params.slug);
            title = `${entityName} - ${title}`;
        } else if (this.currentRoute.params.id) {
            title = `${I18n.t('content.quote')} #${this.currentRoute.params.id} - ${title}`;
        } else if (this.currentRoute.params.query) {
            const query = decodeURIComponent(this.currentRoute.params.query);
            title = `"${query}" - ${title}`;
        }
        
        return `${title} - ${siteName}`;
    },
    
    /**
     * 格式化slug为显示名称
     * @param {string} slug - URL slug
     * @returns {string} - 格式化后的名称
     */
    formatSlugToName: function(slug) {
        return slug
            .replace(/-/g, ' ')
            .replace(/\b\w/g, l => l.toUpperCase());
    },
    
    /**
     * 更新meta标签
     * @param {string} name - 标签名称
     * @param {string} content - 标签内容
     * @param {string} attribute - 属性名称 ('name' | 'property')
     */
    updateMetaTag: function(name, content, attribute = 'name') {
        let meta = document.querySelector(`meta[${attribute}="${name}"]`);
        
        if (!meta) {
            meta = document.createElement('meta');
            meta.setAttribute(attribute, name);
            document.head.appendChild(meta);
        }
        
        meta.setAttribute('content', content);
    },
    
    /**
     * 更新canonical链接
     */
    updateCanonicalLink: function() {
        let canonical = document.querySelector('link[rel="canonical"]');
        
        if (!canonical) {
            canonical = document.createElement('link');
            canonical.setAttribute('rel', 'canonical');
            document.head.appendChild(canonical);
        }
        
        canonical.setAttribute('href', window.location.href);
    },
    
    /**
     * 更新语言链接
     */
    updateLanguageLinks: function() {
        // 移除现有的语言链接
        const existingLinks = document.querySelectorAll('link[rel="alternate"][hreflang]');
        existingLinks.forEach(link => link.remove());
        
        // 添加新的语言链接
        const languages = ['zh', 'en'];
        const currentPath = this.currentRoute.path;
        
        languages.forEach(lang => {
            const link = document.createElement('link');
            link.setAttribute('rel', 'alternate');
            link.setAttribute('hreflang', lang === 'zh' ? 'zh-CN' : 'en-US');
            
            if (lang === 'zh') {
                link.setAttribute('href', `${window.location.origin}${currentPath}`);
            } else {
                link.setAttribute('href', `${window.location.origin}/${lang}${currentPath}`);
            }
            
            document.head.appendChild(link);
        });
        
        // 添加x-default链接
        const defaultLink = document.createElement('link');
        defaultLink.setAttribute('rel', 'alternate');
        defaultLink.setAttribute('hreflang', 'x-default');
        defaultLink.setAttribute('href', `${window.location.origin}${currentPath}`);
        document.head.appendChild(defaultLink);
    },
    
    /**
     * 设置事件监听
     */
    setupEventListeners: function() {
        // 监听popstate事件（浏览器前进后退）
        window.addEventListener('popstate', () => {
            this.parseCurrentRoute();
            this.updatePageMeta();
            this.handleRouteChange();
        });
        
        // 监听语言切换事件
        document.addEventListener('languageChanged', () => {
            this.parseCurrentRoute();
            this.updatePageMeta();
        });
    },
    
    /**
     * 处理路由变化
     */
    handleRouteChange: function() {
        // 触发路由变化事件
        const event = new CustomEvent('routeChanged', {
            detail: {
                route: this.currentRoute,
                path: this.currentRoute.path,
                params: this.currentRoute.params
            }
        });
        
        document.dispatchEvent(event);
    },
    
    /**
     * 导航到指定路径
     * @param {string} path - 目标路径
     * @param {boolean} replace - 是否替换当前历史记录
     */
    navigate: function(path, replace = false) {
        const url = new URL(path, window.location.origin);
        
        if (replace) {
            window.history.replaceState({}, '', url);
        } else {
            window.history.pushState({}, '', url);
        }
        
        this.parseCurrentRoute();
        this.updatePageMeta();
        this.handleRouteChange();
    },
    
    /**
     * 获取当前路由信息
     * @returns {Object} - 当前路由
     */
    getCurrentRoute: function() {
        return this.currentRoute;
    },
    
    /**
     * 检查是否为指定页面
     * @param {string} page - 页面名称
     * @param {string} view - 视图名称（可选）
     * @returns {boolean} - 是否匹配
     */
    isCurrentPage: function(page, view = null) {
        if (!this.currentRoute) return false;
        
        if (view) {
            return this.currentRoute.page === page && this.currentRoute.view === view;
        }
        
        return this.currentRoute.page === page;
    },
    
    /**
     * 生成面包屑数据
     * @returns {Array} - 面包屑数组
     */
    generateBreadcrumbs: function() {
        if (typeof BreadcrumbComponent !== 'undefined') {
            return BreadcrumbComponent.generateBreadcrumbs();
        }
        return [];
    }
};

// 自动初始化
if (typeof window !== 'undefined') {
    document.addEventListener('DOMContentLoaded', () => {
        Router.init();
    });
}
