#!/bin/bash

# SEO优化部署测试脚本 - 2025-01-16
# 用于测试部署后的功能是否正常

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置
DOMAIN="localhost"  # 修改为实际域名
BASE_URL="http://$DOMAIN"

echo -e "${BLUE}=== QuoteSe SEO优化部署测试 ===${NC}"
echo -e "${BLUE}测试域名: $DOMAIN${NC}"
echo ""

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 测试函数
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_result="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -n "测试 $TOTAL_TESTS: $test_name ... "
    
    if eval "$test_command" > /dev/null 2>&1; then
        echo -e "${GREEN}通过${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        echo -e "${RED}失败${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

# HTTP状态码测试
test_http_status() {
    local url="$1"
    local expected_status="$2"
    local actual_status=$(curl -s -o /dev/null -w "%{http_code}" "$url")
    [ "$actual_status" = "$expected_status" ]
}

# 内容包含测试
test_content_contains() {
    local url="$1"
    local expected_content="$2"
    curl -s "$url" | grep -q "$expected_content"
}

# 重定向测试
test_redirect() {
    local url="$1"
    local expected_location="$2"
    local actual_location=$(curl -s -I "$url" | grep -i "location:" | cut -d' ' -f2 | tr -d '\r')
    [[ "$actual_location" == *"$expected_location"* ]]
}

echo -e "${YELLOW}开始测试...${NC}"
echo ""

# 1. 基础页面访问测试
echo -e "${BLUE}1. 基础页面访问测试${NC}"
run_test "首页访问" "test_http_status '$BASE_URL/' '200'"
run_test "作者列表页面" "test_http_status '$BASE_URL/authors/' '200'"
run_test "英文首页" "test_http_status '$BASE_URL/en/' '200'"
run_test "英文作者页面" "test_http_status '$BASE_URL/en/authors/' '200'"

# 2. URL重定向测试
echo ""
echo -e "${BLUE}2. URL重定向测试${NC}"
run_test "旧作者页面重定向" "test_http_status '$BASE_URL/author.html?id=1&name=shakespeare' '301'"
run_test "旧类别页面重定向" "test_http_status '$BASE_URL/category.html?id=1&name=philosophy' '301'"
run_test "旧来源页面重定向" "test_http_status '$BASE_URL/source.html?id=1&name=republic' '301'"
run_test "旧名言页面重定向" "test_http_status '$BASE_URL/quote.html?id=123' '301'"

# 3. 内容验证测试
echo ""
echo -e "${BLUE}3. 内容验证测试${NC}"
run_test "首页包含正确标题" "test_content_contains '$BASE_URL/' 'QuoteSe'"
run_test "作者页面包含面包屑" "test_content_contains '$BASE_URL/authors/' 'breadcrumb'"
run_test "页面包含语言切换" "test_content_contains '$BASE_URL/' 'language-switcher'"
run_test "页面包含结构化数据" "test_content_contains '$BASE_URL/' 'application/ld+json'"

# 4. SEO元素测试
echo ""
echo -e "${BLUE}4. SEO元素测试${NC}"
run_test "页面包含canonical链接" "test_content_contains '$BASE_URL/' 'rel=\"canonical\"'"
run_test "页面包含Open Graph标签" "test_content_contains '$BASE_URL/' 'property=\"og:title\"'"
run_test "页面包含Twitter Card标签" "test_content_contains '$BASE_URL/' 'name=\"twitter:card\"'"
run_test "页面包含语言标签" "test_content_contains '$BASE_URL/' 'hreflang'"

# 5. 多语言功能测试
echo ""
echo -e "${BLUE}5. 多语言功能测试${NC}"
run_test "中文页面语言属性" "test_content_contains '$BASE_URL/' 'lang=\"zh-CN\"'"
run_test "英文页面语言属性" "test_content_contains '$BASE_URL/en/' 'lang=\"en-US\"'"
run_test "中文页面包含中文内容" "test_content_contains '$BASE_URL/' '名言'"
run_test "英文页面包含英文内容" "test_content_contains '$BASE_URL/en/' 'Quotes'"

# 6. JavaScript功能测试
echo ""
echo -e "${BLUE}6. JavaScript文件加载测试${NC}"
run_test "国际化模块加载" "test_http_status '$BASE_URL/js/i18n/i18n.js' '200'"
run_test "路由模块加载" "test_http_status '$BASE_URL/js/core/router.js' '200'"
run_test "SEO模块加载" "test_http_status '$BASE_URL/js/seo/meta-tags.js' '200'"
run_test "面包屑组件加载" "test_http_status '$BASE_URL/js/components/breadcrumb.js' '200'"

# 7. 移动端响应式测试
echo ""
echo -e "${BLUE}7. 移动端响应式测试${NC}"
run_test "移动端viewport设置" "test_content_contains '$BASE_URL/' 'viewport.*width=device-width'"
run_test "响应式CSS加载" "test_content_contains '$BASE_URL/' 'tailwindcss'"
run_test "移动端菜单元素" "test_content_contains '$BASE_URL/' 'mobile-menu'"

# 8. 性能和安全测试
echo ""
echo -e "${BLUE}8. 性能和安全测试${NC}"
run_test "Gzip压缩启用" "curl -H 'Accept-Encoding: gzip' -s -I '$BASE_URL/' | grep -q 'Content-Encoding: gzip'"
run_test "缓存头设置" "curl -s -I '$BASE_URL/css/styles.css' | grep -q 'Cache-Control'"
run_test "安全头设置" "curl -s -I '$BASE_URL/' | grep -q 'X-'"

# 9. 搜索引擎友好性测试
echo ""
echo -e "${BLUE}9. 搜索引擎友好性测试${NC}"
run_test "robots.txt可访问" "test_http_status '$BASE_URL/robots.txt' '200'"
run_test "sitemap.xml可访问" "test_http_status '$BASE_URL/sitemap.xml' '200'"
run_test "页面包含meta描述" "test_content_contains '$BASE_URL/' 'name=\"description\"'"
run_test "页面包含关键词" "test_content_contains '$BASE_URL/' 'name=\"keywords\"'"

# 10. API接口测试
echo ""
echo -e "${BLUE}10. API接口测试${NC}"
run_test "API端点可访问" "test_http_status '$BASE_URL/api/' '200'"
run_test "GraphQL端点可访问" "test_http_status '$BASE_URL/graphql/' '200'"

# 测试结果汇总
echo ""
echo -e "${BLUE}=== 测试结果汇总 ===${NC}"
echo -e "总测试数: $TOTAL_TESTS"
echo -e "${GREEN}通过: $PASSED_TESTS${NC}"
echo -e "${RED}失败: $FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo ""
    echo -e "${GREEN}🎉 所有测试通过！部署成功！${NC}"
    echo ""
    echo -e "${BLUE}建议进行的后续检查:${NC}"
    echo -e "${BLUE}1. 使用Google PageSpeed Insights测试性能${NC}"
    echo -e "${BLUE}2. 使用Google Search Console验证结构化数据${NC}"
    echo -e "${BLUE}3. 使用不同设备和浏览器测试兼容性${NC}"
    echo -e "${BLUE}4. 监控服务器日志和错误报告${NC}"
    echo -e "${BLUE}5. 检查搜索引擎爬虫访问情况${NC}"
    exit 0
else
    echo ""
    echo -e "${RED}❌ 有 $FAILED_TESTS 个测试失败，请检查部署${NC}"
    echo ""
    echo -e "${YELLOW}故障排除建议:${NC}"
    echo -e "${YELLOW}1. 检查Nginx配置是否正确加载${NC}"
    echo -e "${YELLOW}2. 验证文件权限设置${NC}"
    echo -e "${YELLOW}3. 查看Nginx错误日志: sudo tail -f /var/log/nginx/error.log${NC}"
    echo -e "${YELLOW}4. 检查JavaScript控制台错误${NC}"
    echo -e "${YELLOW}5. 验证API服务是否正常运行${NC}"
    exit 1
fi
