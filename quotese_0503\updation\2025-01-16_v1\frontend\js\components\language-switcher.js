/**
 * 语言切换组件 - 2025-01-16
 * 提供多语言切换功能和用户界面
 */

const LanguageSwitcherComponent = {
    
    /**
     * 组件名称
     */
    name: 'language-switcher',
    
    /**
     * 默认配置
     */
    defaultConfig: {
        style: 'dropdown', // 'dropdown' | 'buttons' | 'select'
        showFlags: true,
        showNames: true,
        position: 'top-right',
        className: 'language-switcher'
    },
    
    /**
     * 支持的语言
     */
    languages: [
        {
            code: 'zh',
            name: '中文',
            nativeName: '中文',
            flag: '🇨🇳',
            locale: 'zh-CN'
        },
        {
            code: 'en',
            name: 'English',
            nativeName: 'English',
            flag: '🇺🇸',
            locale: 'en-US'
        }
    ],
    
    /**
     * 渲染语言切换器
     * @param {HTMLElement} container - 容器元素
     * @param {Object} config - 配置选项
     */
    render: function(container, config = {}) {
        const finalConfig = { ...this.defaultConfig, ...config };
        const currentLanguage = UrlHandler.currentLanguage || 'zh';
        
        // 生成HTML
        let html = '';
        switch (finalConfig.style) {
            case 'dropdown':
                html = this.generateDropdownHTML(currentLanguage, finalConfig);
                break;
            case 'buttons':
                html = this.generateButtonsHTML(currentLanguage, finalConfig);
                break;
            case 'select':
                html = this.generateSelectHTML(currentLanguage, finalConfig);
                break;
            default:
                html = this.generateDropdownHTML(currentLanguage, finalConfig);
        }
        
        container.innerHTML = html;
        
        // 添加事件监听
        this.addEventListeners(container, finalConfig);
        
        // 添加CSS类
        container.classList.add(finalConfig.className);
        if (finalConfig.position) {
            container.classList.add(`position-${finalConfig.position}`);
        }
    },
    
    /**
     * 生成下拉菜单HTML
     * @param {string} currentLanguage - 当前语言
     * @param {Object} config - 配置
     * @returns {string} - HTML字符串
     */
    generateDropdownHTML: function(currentLanguage, config) {
        const currentLang = this.languages.find(lang => lang.code === currentLanguage);
        
        let html = '<div class="language-dropdown">';
        
        // 当前语言按钮
        html += '<button class="language-current" aria-haspopup="true" aria-expanded="false" ';
        html += `aria-label="${I18n.t('a11y.language_selector')}" title="${I18n.t('language.switch_to', { language: '' })}">`;
        
        if (config.showFlags && currentLang.flag) {
            html += `<span class="language-flag" aria-hidden="true">${currentLang.flag}</span>`;
        }
        
        if (config.showNames) {
            html += `<span class="language-name">${currentLang.nativeName}</span>`;
        }
        
        html += '<span class="language-arrow" aria-hidden="true">▼</span>';
        html += '</button>';
        
        // 下拉菜单
        html += '<ul class="language-menu" role="menu" aria-hidden="true">';
        
        this.languages.forEach(lang => {
            const isActive = lang.code === currentLanguage;
            const itemClass = isActive ? 'language-item active' : 'language-item';
            
            html += `<li class="${itemClass}" role="none">`;
            html += `<button class="language-option" role="menuitem" data-language="${lang.code}" `;
            html += `aria-label="${I18n.t('language.switch_to', { language: lang.name })}" `;
            html += `${isActive ? 'aria-current="true"' : ''}`;
            html += `title="${I18n.t('language.switch_to', { language: lang.name })}">`;
            
            if (config.showFlags && lang.flag) {
                html += `<span class="language-flag" aria-hidden="true">${lang.flag}</span>`;
            }
            
            if (config.showNames) {
                html += `<span class="language-name">${lang.nativeName}</span>`;
            }
            
            if (isActive) {
                html += '<span class="language-check" aria-hidden="true">✓</span>';
            }
            
            html += '</button>';
            html += '</li>';
        });
        
        html += '</ul>';
        html += '</div>';
        
        return html;
    },
    
    /**
     * 生成按钮组HTML
     * @param {string} currentLanguage - 当前语言
     * @param {Object} config - 配置
     * @returns {string} - HTML字符串
     */
    generateButtonsHTML: function(currentLanguage, config) {
        let html = '<div class="language-buttons" role="group" ';
        html += `aria-label="${I18n.t('a11y.language_selector')}">`;
        
        this.languages.forEach((lang, index) => {
            const isActive = lang.code === currentLanguage;
            const buttonClass = isActive ? 'language-button active' : 'language-button';
            
            html += `<button class="${buttonClass}" data-language="${lang.code}" `;
            html += `aria-label="${I18n.t('language.switch_to', { language: lang.name })}" `;
            html += `${isActive ? 'aria-current="true"' : ''}`;
            html += `title="${I18n.t('language.switch_to', { language: lang.name })}">`;
            
            if (config.showFlags && lang.flag) {
                html += `<span class="language-flag" aria-hidden="true">${lang.flag}</span>`;
            }
            
            if (config.showNames) {
                html += `<span class="language-name">${lang.nativeName}</span>`;
            }
            
            html += '</button>';
        });
        
        html += '</div>';
        
        return html;
    },
    
    /**
     * 生成选择框HTML
     * @param {string} currentLanguage - 当前语言
     * @param {Object} config - 配置
     * @returns {string} - HTML字符串
     */
    generateSelectHTML: function(currentLanguage, config) {
        let html = '<div class="language-select-wrapper">';
        html += `<select class="language-select" aria-label="${I18n.t('a11y.language_selector')}">`;
        
        this.languages.forEach(lang => {
            const isSelected = lang.code === currentLanguage;
            html += `<option value="${lang.code}" ${isSelected ? 'selected' : ''}>`;
            
            if (config.showFlags && lang.flag) {
                html += `${lang.flag} `;
            }
            
            html += lang.nativeName;
            html += '</option>';
        });
        
        html += '</select>';
        html += '</div>';
        
        return html;
    },
    
    /**
     * 添加事件监听
     * @param {HTMLElement} container - 容器元素
     * @param {Object} config - 配置
     */
    addEventListeners: function(container, config) {
        if (config.style === 'dropdown') {
            this.addDropdownListeners(container);
        } else if (config.style === 'buttons') {
            this.addButtonsListeners(container);
        } else if (config.style === 'select') {
            this.addSelectListeners(container);
        }
        
        // 键盘导航支持
        this.addKeyboardListeners(container);
    },
    
    /**
     * 添加下拉菜单事件监听
     * @param {HTMLElement} container - 容器元素
     */
    addDropdownListeners: function(container) {
        const currentButton = container.querySelector('.language-current');
        const menu = container.querySelector('.language-menu');
        const options = container.querySelectorAll('.language-option');
        
        if (!currentButton || !menu) return;
        
        // 切换下拉菜单
        currentButton.addEventListener('click', (e) => {
            e.stopPropagation();
            const isExpanded = currentButton.getAttribute('aria-expanded') === 'true';
            this.toggleDropdown(currentButton, menu, !isExpanded);
        });
        
        // 选择语言
        options.forEach(option => {
            option.addEventListener('click', (e) => {
                e.stopPropagation();
                const language = option.getAttribute('data-language');
                this.switchLanguage(language);
                this.toggleDropdown(currentButton, menu, false);
            });
        });
        
        // 点击外部关闭
        document.addEventListener('click', () => {
            this.toggleDropdown(currentButton, menu, false);
        });
        
        // ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.toggleDropdown(currentButton, menu, false);
                currentButton.focus();
            }
        });
    },
    
    /**
     * 添加按钮组事件监听
     * @param {HTMLElement} container - 容器元素
     */
    addButtonsListeners: function(container) {
        const buttons = container.querySelectorAll('.language-button');
        
        buttons.forEach(button => {
            button.addEventListener('click', (e) => {
                const language = button.getAttribute('data-language');
                this.switchLanguage(language);
            });
        });
    },
    
    /**
     * 添加选择框事件监听
     * @param {HTMLElement} container - 容器元素
     */
    addSelectListeners: function(container) {
        const select = container.querySelector('.language-select');
        
        if (select) {
            select.addEventListener('change', (e) => {
                const language = e.target.value;
                this.switchLanguage(language);
            });
        }
    },
    
    /**
     * 添加键盘导航支持
     * @param {HTMLElement} container - 容器元素
     */
    addKeyboardListeners: function(container) {
        const menu = container.querySelector('.language-menu');
        if (!menu) return;
        
        menu.addEventListener('keydown', (e) => {
            const options = Array.from(menu.querySelectorAll('.language-option'));
            const currentIndex = options.findIndex(option => option === document.activeElement);
            
            switch (e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    const nextIndex = (currentIndex + 1) % options.length;
                    options[nextIndex].focus();
                    break;
                    
                case 'ArrowUp':
                    e.preventDefault();
                    const prevIndex = currentIndex > 0 ? currentIndex - 1 : options.length - 1;
                    options[prevIndex].focus();
                    break;
                    
                case 'Enter':
                case ' ':
                    e.preventDefault();
                    if (currentIndex >= 0) {
                        options[currentIndex].click();
                    }
                    break;
            }
        });
    },
    
    /**
     * 切换下拉菜单状态
     * @param {HTMLElement} button - 按钮元素
     * @param {HTMLElement} menu - 菜单元素
     * @param {boolean} isOpen - 是否打开
     */
    toggleDropdown: function(button, menu, isOpen) {
        button.setAttribute('aria-expanded', isOpen.toString());
        menu.setAttribute('aria-hidden', (!isOpen).toString());
        
        if (isOpen) {
            menu.classList.add('open');
            // 聚焦到第一个选项
            const firstOption = menu.querySelector('.language-option');
            if (firstOption) {
                setTimeout(() => firstOption.focus(), 0);
            }
        } else {
            menu.classList.remove('open');
        }
    },
    
    /**
     * 切换语言
     * @param {string} language - 目标语言
     */
    switchLanguage: function(language) {
        if (UrlHandler && typeof UrlHandler.switchLanguage === 'function') {
            UrlHandler.switchLanguage(language);
        } else {
            // 备用方案：直接修改URL
            const currentUrl = new URL(window.location);
            
            if (language === 'zh') {
                // 中文版本，移除语言前缀
                currentUrl.pathname = currentUrl.pathname.replace(/^\/en\//, '/');
                currentUrl.searchParams.delete('lang');
            } else {
                // 其他语言，添加语言前缀
                if (!currentUrl.pathname.startsWith(`/${language}/`)) {
                    currentUrl.pathname = `/${language}${currentUrl.pathname}`;
                }
                currentUrl.searchParams.delete('lang');
            }
            
            window.location.href = currentUrl.toString();
        }
    },
    
    /**
     * 更新组件状态
     * @param {HTMLElement} container - 容器元素
     */
    update: function(container) {
        const currentLanguage = UrlHandler.currentLanguage || 'zh';
        
        // 更新活动状态
        const activeElements = container.querySelectorAll('.active');
        activeElements.forEach(el => el.classList.remove('active'));
        
        const currentElements = container.querySelectorAll(`[data-language="${currentLanguage}"]`);
        currentElements.forEach(el => {
            el.classList.add('active');
            el.setAttribute('aria-current', 'true');
        });
        
        // 更新选择框
        const select = container.querySelector('.language-select');
        if (select) {
            select.value = currentLanguage;
        }
    },
    
    /**
     * 获取当前语言信息
     * @returns {Object} - 语言信息
     */
    getCurrentLanguage: function() {
        const currentCode = UrlHandler.currentLanguage || 'zh';
        return this.languages.find(lang => lang.code === currentCode);
    }
};

// 注册组件
if (typeof ComponentRegistry !== 'undefined') {
    ComponentRegistry.register(LanguageSwitcherComponent.name, LanguageSwitcherComponent);
}

// 导出组件
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LanguageSwitcherComponent;
}
