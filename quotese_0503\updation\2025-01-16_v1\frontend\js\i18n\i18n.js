/**
 * 国际化支持模块 - 2025-01-16
 * 支持中英文切换和本地化内容
 */

const I18n = {
    
    /**
     * 当前语言
     */
    currentLanguage: 'zh',
    
    /**
     * 语言包
     */
    messages: {},
    
    /**
     * 初始化国际化
     */
    async init() {
        // 检测语言
        this.detectLanguage();
        
        // 加载语言包
        await this.loadMessages();
        
        // 应用翻译
        this.applyTranslations();
        
        // 设置语言属性
        this.setLanguageAttributes();
    },
    
    /**
     * 检测当前语言
     */
    detectLanguage() {
        // 从URL参数获取
        const urlParams = new URLSearchParams(window.location.search);
        const langFromUrl = urlParams.get('lang');
        
        // 从路径获取
        const pathMatch = window.location.pathname.match(/^\/([a-z]{2})\//);
        const langFromPath = pathMatch ? pathMatch[1] : null;
        
        if (langFromUrl && ['zh', 'en'].includes(langFromUrl)) {
            this.currentLanguage = langFromUrl;
        } else if (langFromPath && ['zh', 'en'].includes(langFromPath)) {
            this.currentLanguage = langFromPath;
        } else {
            // 从浏览器语言检测
            const browserLang = navigator.language.toLowerCase();
            this.currentLanguage = browserLang.startsWith('en') ? 'en' : 'zh';
        }
    },
    
    /**
     * 加载语言包
     */
    async loadMessages() {
        try {
            // 加载中文语言包
            const zhResponse = await fetch('/js/i18n/zh.js');
            if (zhResponse.ok) {
                const zhModule = await zhResponse.text();
                eval(zhModule); // 执行语言包文件
                this.messages.zh = window.zhMessages || {};
            }
            
            // 加载英文语言包
            const enResponse = await fetch('/js/i18n/en.js');
            if (enResponse.ok) {
                const enModule = await enResponse.text();
                eval(enModule); // 执行语言包文件
                this.messages.en = window.enMessages || {};
            }
        } catch (error) {
            console.warn('加载语言包失败:', error);
            // 使用默认语言包
            this.loadDefaultMessages();
        }
    },
    
    /**
     * 加载默认语言包
     */
    loadDefaultMessages() {
        this.messages = {
            zh: {
                // 导航
                'nav.home': '首页',
                'nav.authors': '作者',
                'nav.categories': '分类',
                'nav.sources': '来源',
                'nav.search': '搜索',
                
                // 页面标题
                'page.home.title': '名言警句 - 智慧人生',
                'page.authors.title': '名人作者',
                'page.categories.title': '名言分类',
                'page.sources.title': '名言来源',
                'page.search.title': '搜索名言',
                
                // 按钮和操作
                'btn.search': '搜索',
                'btn.more': '更多',
                'btn.back': '返回',
                'btn.share': '分享',
                'btn.copy': '复制',
                
                // 分页
                'pagination.prev': '上一页',
                'pagination.next': '下一页',
                'pagination.page': '第 {page} 页',
                'pagination.total': '共 {total} 条',
                
                // 搜索
                'search.placeholder': '搜索名言、作者、分类...',
                'search.no_results': '没有找到相关结果',
                'search.results_count': '找到 {count} 条结果',
                
                // 错误信息
                'error.404': '页面未找到',
                'error.500': '服务器错误',
                'error.network': '网络连接错误',
                
                // 其他
                'loading': '加载中...',
                'language': '语言',
                'author': '作者',
                'category': '分类',
                'source': '来源',
                'quote': '名言',
                'quotes': '名言'
            },
            
            en: {
                // 导航
                'nav.home': 'Home',
                'nav.authors': 'Authors',
                'nav.categories': 'Categories',
                'nav.sources': 'Sources',
                'nav.search': 'Search',
                
                // 页面标题
                'page.home.title': 'Famous Quotes - Wisdom for Life',
                'page.authors.title': 'Famous Authors',
                'page.categories.title': 'Quote Categories',
                'page.sources.title': 'Quote Sources',
                'page.search.title': 'Search Quotes',
                
                // 按钮和操作
                'btn.search': 'Search',
                'btn.more': 'More',
                'btn.back': 'Back',
                'btn.share': 'Share',
                'btn.copy': 'Copy',
                
                // 分页
                'pagination.prev': 'Previous',
                'pagination.next': 'Next',
                'pagination.page': 'Page {page}',
                'pagination.total': 'Total {total} items',
                
                // 搜索
                'search.placeholder': 'Search quotes, authors, categories...',
                'search.no_results': 'No results found',
                'search.results_count': 'Found {count} results',
                
                // 错误信息
                'error.404': 'Page Not Found',
                'error.500': 'Server Error',
                'error.network': 'Network Connection Error',
                
                // 其他
                'loading': 'Loading...',
                'language': 'Language',
                'author': 'Author',
                'category': 'Category',
                'source': 'Source',
                'quote': 'Quote',
                'quotes': 'Quotes'
            }
        };
    },
    
    /**
     * 获取翻译文本
     * @param {string} key - 翻译键
     * @param {Object} params - 参数对象
     * @returns {string} - 翻译后的文本
     */
    t(key, params = {}) {
        const messages = this.messages[this.currentLanguage] || this.messages.zh;
        let message = messages[key] || key;
        
        // 替换参数
        Object.keys(params).forEach(param => {
            message = message.replace(new RegExp(`\\{${param}\\}`, 'g'), params[param]);
        });
        
        return message;
    },
    
    /**
     * 应用翻译到页面元素
     */
    applyTranslations() {
        // 翻译带有 data-i18n 属性的元素
        const elements = document.querySelectorAll('[data-i18n]');
        elements.forEach(element => {
            const key = element.getAttribute('data-i18n');
            const params = element.getAttribute('data-i18n-params');
            
            let translationParams = {};
            if (params) {
                try {
                    translationParams = JSON.parse(params);
                } catch (e) {
                    console.warn('解析翻译参数失败:', params);
                }
            }
            
            element.textContent = this.t(key, translationParams);
        });
        
        // 翻译带有 data-i18n-placeholder 属性的输入框
        const placeholderElements = document.querySelectorAll('[data-i18n-placeholder]');
        placeholderElements.forEach(element => {
            const key = element.getAttribute('data-i18n-placeholder');
            element.placeholder = this.t(key);
        });
        
        // 翻译带有 data-i18n-title 属性的元素
        const titleElements = document.querySelectorAll('[data-i18n-title]');
        titleElements.forEach(element => {
            const key = element.getAttribute('data-i18n-title');
            element.title = this.t(key);
        });
    },
    
    /**
     * 设置语言相关属性
     */
    setLanguageAttributes() {
        // 设置 html lang 属性
        document.documentElement.lang = this.currentLanguage === 'en' ? 'en-US' : 'zh-CN';
        
        // 设置文档方向（中文和英文都是从左到右）
        document.documentElement.dir = 'ltr';
        
        // 添加语言类名到 body
        document.body.classList.remove('lang-zh', 'lang-en');
        document.body.classList.add(`lang-${this.currentLanguage}`);
    },
    
    /**
     * 切换语言
     * @param {string} language - 目标语言
     */
    async switchLanguage(language) {
        if (!['zh', 'en'].includes(language)) {
            console.warn('不支持的语言:', language);
            return;
        }
        
        this.currentLanguage = language;
        
        // 重新加载语言包（如果需要）
        if (!this.messages[language]) {
            await this.loadMessages();
        }
        
        // 应用翻译
        this.applyTranslations();
        
        // 设置语言属性
        this.setLanguageAttributes();
        
        // 触发语言切换事件
        const event = new CustomEvent('languageChanged', {
            detail: { language: language }
        });
        document.dispatchEvent(event);
    },
    
    /**
     * 获取当前语言
     * @returns {string} - 当前语言代码
     */
    getCurrentLanguage() {
        return this.currentLanguage;
    },
    
    /**
     * 获取语言显示名称
     * @param {string} language - 语言代码
     * @returns {string} - 语言显示名称
     */
    getLanguageName(language) {
        const names = {
            'zh': '中文',
            'en': 'English'
        };
        return names[language] || language;
    },
    
    /**
     * 格式化数字
     * @param {number} number - 数字
     * @returns {string} - 格式化后的数字
     */
    formatNumber(number) {
        if (this.currentLanguage === 'en') {
            return number.toLocaleString('en-US');
        } else {
            return number.toLocaleString('zh-CN');
        }
    },
    
    /**
     * 格式化日期
     * @param {Date} date - 日期对象
     * @returns {string} - 格式化后的日期
     */
    formatDate(date) {
        if (this.currentLanguage === 'en') {
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        } else {
            return date.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        }
    }
};

// 自动初始化
if (typeof window !== 'undefined') {
    document.addEventListener('DOMContentLoaded', () => {
        I18n.init();
    });
}
