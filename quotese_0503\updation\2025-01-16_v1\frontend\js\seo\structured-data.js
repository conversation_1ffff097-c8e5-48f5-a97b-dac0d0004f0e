/**
 * SEO结构化数据生成器 - 2025-01-16
 * 生成符合Schema.org标准的结构化数据
 */

const StructuredData = {
    
    /**
     * 网站基础信息
     */
    siteInfo: {
        name: 'QuoteSe',
        url: 'https://quotese.com',
        description: '收集世界各地的名言警句，传播智慧与哲理',
        logo: 'https://quotese.com/images/logo.png',
        sameAs: [
            'https://twitter.com/quotese',
            'https://facebook.com/quotese'
        ]
    },
    
    /**
     * 生成网站组织结构化数据
     * @returns {Object} - 组织结构化数据
     */
    generateOrganization: function() {
        return {
            "@context": "https://schema.org",
            "@type": "Organization",
            "name": this.siteInfo.name,
            "url": this.siteInfo.url,
            "description": this.siteInfo.description,
            "logo": {
                "@type": "ImageObject",
                "url": this.siteInfo.logo
            },
            "sameAs": this.siteInfo.sameAs
        };
    },
    
    /**
     * 生成网站结构化数据
     * @returns {Object} - 网站结构化数据
     */
    generateWebSite: function() {
        return {
            "@context": "https://schema.org",
            "@type": "WebSite",
            "name": this.siteInfo.name,
            "url": this.siteInfo.url,
            "description": this.siteInfo.description,
            "potentialAction": {
                "@type": "SearchAction",
                "target": {
                    "@type": "EntryPoint",
                    "urlTemplate": `${this.siteInfo.url}/search/{search_term_string}/`
                },
                "query-input": "required name=search_term_string"
            }
        };
    },
    
    /**
     * 生成作者结构化数据
     * @param {Object} author - 作者信息
     * @returns {Object} - 作者结构化数据
     */
    generatePerson: function(author) {
        const data = {
            "@context": "https://schema.org",
            "@type": "Person",
            "name": author.name,
            "url": `${this.siteInfo.url}${UrlHandler.getAuthorUrl(author)}`,
            "description": author.description || `${author.name}的名言警句`
        };
        
        // 添加可选字段
        if (author.birth_date) {
            data.birthDate = author.birth_date;
        }
        
        if (author.death_date) {
            data.deathDate = author.death_date;
        }
        
        if (author.nationality) {
            data.nationality = {
                "@type": "Country",
                "name": author.nationality
            };
        }
        
        if (author.occupation) {
            data.jobTitle = author.occupation;
        }
        
        if (author.image) {
            data.image = {
                "@type": "ImageObject",
                "url": author.image
            };
        }
        
        return data;
    },
    
    /**
     * 生成名言结构化数据
     * @param {Object} quote - 名言信息
     * @returns {Object} - 名言结构化数据
     */
    generateQuotation: function(quote) {
        const data = {
            "@context": "https://schema.org",
            "@type": "Quotation",
            "text": quote.content,
            "url": `${this.siteInfo.url}${UrlHandler.getQuoteUrl(quote)}`,
            "datePublished": quote.created_at || new Date().toISOString(),
            "inLanguage": quote.language || 'zh-CN'
        };
        
        // 添加作者信息
        if (quote.author) {
            data.author = {
                "@type": "Person",
                "name": quote.author.name,
                "url": `${this.siteInfo.url}${UrlHandler.getAuthorUrl(quote.author)}`
            };
        }
        
        // 添加来源信息
        if (quote.sources && quote.sources.length > 0) {
            data.isPartOf = quote.sources.map(source => ({
                "@type": "CreativeWork",
                "name": source.name,
                "url": `${this.siteInfo.url}${UrlHandler.getSourceUrl(source)}`
            }));
        }
        
        // 添加类别信息
        if (quote.categories && quote.categories.length > 0) {
            data.about = quote.categories.map(category => ({
                "@type": "Thing",
                "name": category.name,
                "url": `${this.siteInfo.url}${UrlHandler.getCategoryUrl(category)}`
            }));
        }
        
        return data;
    },
    
    /**
     * 生成文章结构化数据（用于详情页面）
     * @param {Object} content - 内容信息
     * @returns {Object} - 文章结构化数据
     */
    generateArticle: function(content) {
        const data = {
            "@context": "https://schema.org",
            "@type": "Article",
            "headline": content.title,
            "description": content.description,
            "url": content.url,
            "datePublished": content.datePublished || new Date().toISOString(),
            "dateModified": content.dateModified || content.datePublished || new Date().toISOString(),
            "inLanguage": content.language || 'zh-CN',
            "publisher": {
                "@type": "Organization",
                "name": this.siteInfo.name,
                "url": this.siteInfo.url,
                "logo": {
                    "@type": "ImageObject",
                    "url": this.siteInfo.logo
                }
            }
        };
        
        // 添加作者信息
        if (content.author) {
            data.author = {
                "@type": "Person",
                "name": content.author.name
            };
        }
        
        // 添加图片信息
        if (content.image) {
            data.image = {
                "@type": "ImageObject",
                "url": content.image
            };
        }
        
        return data;
    },
    
    /**
     * 生成面包屑结构化数据
     * @param {Array} breadcrumbs - 面包屑数组
     * @returns {Object} - 面包屑结构化数据
     */
    generateBreadcrumbList: function(breadcrumbs) {
        return {
            "@context": "https://schema.org",
            "@type": "BreadcrumbList",
            "itemListElement": breadcrumbs.map(item => ({
                "@type": "ListItem",
                "position": item.position,
                "name": item.name,
                "item": `${this.siteInfo.url}${item.url}`
            }))
        };
    },
    
    /**
     * 生成搜索结果页面结构化数据
     * @param {string} query - 搜索查询
     * @param {Array} results - 搜索结果
     * @returns {Object} - 搜索结果结构化数据
     */
    generateSearchResultsPage: function(query, results) {
        return {
            "@context": "https://schema.org",
            "@type": "SearchResultsPage",
            "name": `"${query}"的搜索结果`,
            "url": `${this.siteInfo.url}/search/${encodeURIComponent(query)}/`,
            "mainEntity": {
                "@type": "ItemList",
                "numberOfItems": results.length,
                "itemListElement": results.map((result, index) => ({
                    "@type": "ListItem",
                    "position": index + 1,
                    "item": {
                        "@type": result.type === 'quote' ? 'Quotation' : 'Thing',
                        "name": result.title,
                        "url": result.url,
                        "description": result.description
                    }
                }))
            }
        };
    },
    
    /**
     * 生成FAQ结构化数据
     * @param {Array} faqs - FAQ数组
     * @returns {Object} - FAQ结构化数据
     */
    generateFAQPage: function(faqs) {
        return {
            "@context": "https://schema.org",
            "@type": "FAQPage",
            "mainEntity": faqs.map(faq => ({
                "@type": "Question",
                "name": faq.question,
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": faq.answer
                }
            }))
        };
    },
    
    /**
     * 添加结构化数据到页面
     * @param {Object} data - 结构化数据
     * @param {string} id - 脚本ID（可选）
     */
    addToPage: function(data, id = null) {
        // 移除现有的相同ID脚本
        if (id) {
            const existingScript = document.getElementById(id);
            if (existingScript) {
                existingScript.remove();
            }
        }
        
        // 创建新的脚本标签
        const script = document.createElement('script');
        script.type = 'application/ld+json';
        if (id) {
            script.id = id;
        }
        script.textContent = JSON.stringify(data, null, 2);
        
        // 添加到页面头部
        document.head.appendChild(script);
    },
    
    /**
     * 移除结构化数据
     * @param {string} id - 脚本ID
     */
    removeFromPage: function(id) {
        const script = document.getElementById(id);
        if (script) {
            script.remove();
        }
    },
    
    /**
     * 根据页面类型自动生成结构化数据
     * @param {Object} pageData - 页面数据
     */
    autoGenerate: function(pageData) {
        // 清除现有的结构化数据
        const existingScripts = document.querySelectorAll('script[type="application/ld+json"]');
        existingScripts.forEach(script => {
            if (!script.hasAttribute('data-keep')) {
                script.remove();
            }
        });
        
        // 添加基础网站信息
        this.addToPage(this.generateWebSite(), 'website-schema');
        this.addToPage(this.generateOrganization(), 'organization-schema');
        
        // 根据页面类型添加特定结构化数据
        const route = Router.getCurrentRoute();
        if (!route) return;
        
        switch (route.page) {
            case 'authors':
                if (route.view === 'detail' && pageData.author) {
                    this.addToPage(this.generatePerson(pageData.author), 'person-schema');
                }
                break;
                
            case 'quotes':
                if (route.view === 'detail' && pageData.quote) {
                    this.addToPage(this.generateQuotation(pageData.quote), 'quotation-schema');
                }
                break;
                
            case 'search':
                if (route.view === 'results' && pageData.query && pageData.results) {
                    this.addToPage(
                        this.generateSearchResultsPage(pageData.query, pageData.results),
                        'search-results-schema'
                    );
                }
                break;
        }
        
        // 添加面包屑结构化数据
        const breadcrumbs = Router.generateBreadcrumbs();
        if (breadcrumbs && breadcrumbs.length > 1) {
            this.addToPage(this.generateBreadcrumbList(breadcrumbs), 'breadcrumb-schema');
        }
    },
    
    /**
     * 验证结构化数据
     * @param {Object} data - 结构化数据
     * @returns {boolean} - 是否有效
     */
    validate: function(data) {
        try {
            // 基本验证
            if (!data['@context'] || !data['@type']) {
                console.warn('结构化数据缺少必要的@context或@type字段');
                return false;
            }
            
            // 检查JSON格式
            JSON.stringify(data);
            
            return true;
        } catch (error) {
            console.error('结构化数据验证失败:', error);
            return false;
        }
    }
};

// 监听路由变化，自动更新结构化数据
if (typeof document !== 'undefined') {
    document.addEventListener('routeChanged', (event) => {
        // 延迟执行，确保页面数据已加载
        setTimeout(() => {
            const pageData = window.currentPageData || {};
            StructuredData.autoGenerate(pageData);
        }, 100);
    });
}
