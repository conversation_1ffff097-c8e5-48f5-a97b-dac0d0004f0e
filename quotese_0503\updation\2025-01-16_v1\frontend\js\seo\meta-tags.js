/**
 * 动态Meta标签管理器 - 2025-01-16
 * 管理页面的SEO meta标签，包括Open Graph和Twitter Card
 */

const MetaTags = {
    
    /**
     * 默认meta标签配置
     */
    defaults: {
        title: 'QuoteSe - 名言警句网',
        description: '收集世界各地的名言警句，传播智慧与哲理',
        keywords: '名言,警句,格言,智慧,哲理,人生感悟',
        author: 'QuoteSe',
        robots: 'index,follow',
        viewport: 'width=device-width, initial-scale=1.0',
        charset: 'UTF-8',
        
        // Open Graph
        ogType: 'website',
        ogSiteName: 'QuoteSe',
        ogLocale: 'zh_CN',
        ogImage: 'https://quotese.com/images/og-image.jpg',
        ogImageWidth: '1200',
        ogImageHeight: '630',
        
        // Twitter Card
        twitterCard: 'summary_large_image',
        twitterSite: '@quotese',
        twitterCreator: '@quotese',
        
        // 其他
        themeColor: '#2c3e50',
        msapplicationTileColor: '#2c3e50'
    },
    
    /**
     * 初始化meta标签管理器
     */
    init: function() {
        this.setBasicMeta();
        this.setupEventListeners();
    },
    
    /**
     * 设置基础meta标签
     */
    setBasicMeta: function() {
        // 设置字符编码
        this.setMeta('charset', this.defaults.charset, 'charset');
        
        // 设置viewport
        this.setMeta('viewport', this.defaults.viewport);
        
        // 设置robots
        this.setMeta('robots', this.defaults.robots);
        
        // 设置author
        this.setMeta('author', this.defaults.author);
        
        // 设置theme-color
        this.setMeta('theme-color', this.defaults.themeColor);
        
        // 设置msapplication-TileColor
        this.setMeta('msapplication-TileColor', this.defaults.msapplicationTileColor);
    },
    
    /**
     * 设置页面meta标签
     * @param {Object} config - 页面配置
     */
    setPageMeta: function(config = {}) {
        const finalConfig = { ...this.defaults, ...config };
        
        // 设置页面标题
        document.title = finalConfig.title;
        
        // 设置基本meta标签
        this.setMeta('description', finalConfig.description);
        this.setMeta('keywords', finalConfig.keywords);
        
        // 设置Open Graph标签
        this.setOpenGraphMeta(finalConfig);
        
        // 设置Twitter Card标签
        this.setTwitterCardMeta(finalConfig);
        
        // 设置canonical链接
        this.setCanonicalLink(finalConfig.canonical || window.location.href);
        
        // 设置语言相关标签
        this.setLanguageMeta(finalConfig);
    },
    
    /**
     * 设置Open Graph meta标签
     * @param {Object} config - 配置对象
     */
    setOpenGraphMeta: function(config) {
        this.setMeta('og:title', config.title, 'property');
        this.setMeta('og:description', config.description, 'property');
        this.setMeta('og:type', config.ogType, 'property');
        this.setMeta('og:url', config.canonical || window.location.href, 'property');
        this.setMeta('og:site_name', config.ogSiteName, 'property');
        this.setMeta('og:locale', config.ogLocale, 'property');
        
        // 设置图片
        if (config.ogImage) {
            this.setMeta('og:image', config.ogImage, 'property');
            this.setMeta('og:image:width', config.ogImageWidth, 'property');
            this.setMeta('og:image:height', config.ogImageHeight, 'property');
            this.setMeta('og:image:alt', config.title, 'property');
        }
        
        // 设置其他语言版本
        if (config.ogLocaleAlternate) {
            config.ogLocaleAlternate.forEach(locale => {
                this.addMeta('og:locale:alternate', locale, 'property');
            });
        }
    },
    
    /**
     * 设置Twitter Card meta标签
     * @param {Object} config - 配置对象
     */
    setTwitterCardMeta: function(config) {
        this.setMeta('twitter:card', config.twitterCard);
        this.setMeta('twitter:site', config.twitterSite);
        this.setMeta('twitter:creator', config.twitterCreator);
        this.setMeta('twitter:title', config.title);
        this.setMeta('twitter:description', config.description);
        
        if (config.ogImage) {
            this.setMeta('twitter:image', config.ogImage);
            this.setMeta('twitter:image:alt', config.title);
        }
    },
    
    /**
     * 设置语言相关meta标签
     * @param {Object} config - 配置对象
     */
    setLanguageMeta: function(config) {
        const language = config.language || UrlHandler.currentLanguage || 'zh';
        const locale = language === 'en' ? 'en-US' : 'zh-CN';
        
        // 设置html lang属性
        document.documentElement.lang = locale;
        
        // 设置content-language
        this.setMeta('content-language', locale, 'http-equiv');
        
        // 更新Open Graph locale
        this.setMeta('og:locale', locale.replace('-', '_'), 'property');
    },
    
    /**
     * 设置canonical链接
     * @param {string} url - canonical URL
     */
    setCanonicalLink: function(url) {
        let canonical = document.querySelector('link[rel="canonical"]');
        
        if (!canonical) {
            canonical = document.createElement('link');
            canonical.rel = 'canonical';
            document.head.appendChild(canonical);
        }
        
        canonical.href = url;
    },
    
    /**
     * 设置meta标签
     * @param {string} name - 标签名称
     * @param {string} content - 标签内容
     * @param {string} attribute - 属性名称
     */
    setMeta: function(name, content, attribute = 'name') {
        if (!content) return;
        
        let meta = document.querySelector(`meta[${attribute}="${name}"]`);
        
        if (!meta) {
            meta = document.createElement('meta');
            meta.setAttribute(attribute, name);
            document.head.appendChild(meta);
        }
        
        meta.content = content;
    },
    
    /**
     * 添加meta标签（允许多个相同名称的标签）
     * @param {string} name - 标签名称
     * @param {string} content - 标签内容
     * @param {string} attribute - 属性名称
     */
    addMeta: function(name, content, attribute = 'name') {
        if (!content) return;
        
        const meta = document.createElement('meta');
        meta.setAttribute(attribute, name);
        meta.content = content;
        document.head.appendChild(meta);
    },
    
    /**
     * 移除meta标签
     * @param {string} name - 标签名称
     * @param {string} attribute - 属性名称
     */
    removeMeta: function(name, attribute = 'name') {
        const metas = document.querySelectorAll(`meta[${attribute}="${name}"]`);
        metas.forEach(meta => meta.remove());
    },
    
    /**
     * 根据页面类型生成meta配置
     * @param {Object} pageData - 页面数据
     * @returns {Object} - meta配置
     */
    generatePageMeta: function(pageData) {
        const route = Router.getCurrentRoute();
        if (!route) return this.defaults;
        
        const config = { ...this.defaults };
        const language = route.language || 'zh';
        
        // 根据页面类型设置meta
        switch (route.page) {
            case 'index':
                config.title = I18n.t('page.home.title') + ' - ' + I18n.t('site.title');
                config.description = I18n.t('page.home.description');
                config.ogType = 'website';
                break;
                
            case 'authors':
                if (route.view === 'detail' && pageData.author) {
                    config.title = `${pageData.author.name} - ${I18n.t('page.authors.title')} - ${I18n.t('site.title')}`;
                    config.description = pageData.author.description || `${pageData.author.name}的名言警句集合`;
                    config.keywords = `${pageData.author.name},名言,警句,${config.keywords}`;
                    config.ogType = 'profile';
                } else {
                    config.title = I18n.t('page.authors.title') + ' - ' + I18n.t('site.title');
                    config.description = I18n.t('page.authors.description');
                }
                break;
                
            case 'categories':
                if (route.view === 'detail' && pageData.category) {
                    config.title = `${pageData.category.name} - ${I18n.t('page.categories.title')} - ${I18n.t('site.title')}`;
                    config.description = pageData.category.description || `关于${pageData.category.name}的名言警句`;
                    config.keywords = `${pageData.category.name},${config.keywords}`;
                } else {
                    config.title = I18n.t('page.categories.title') + ' - ' + I18n.t('site.title');
                    config.description = I18n.t('page.categories.description');
                }
                break;
                
            case 'sources':
                if (route.view === 'detail' && pageData.source) {
                    config.title = `${pageData.source.name} - ${I18n.t('page.sources.title')} - ${I18n.t('site.title')}`;
                    config.description = pageData.source.description || `来自${pageData.source.name}的名言警句`;
                    config.keywords = `${pageData.source.name},${config.keywords}`;
                } else {
                    config.title = I18n.t('page.sources.title') + ' - ' + I18n.t('site.title');
                    config.description = I18n.t('page.sources.description');
                }
                break;
                
            case 'quotes':
                if (route.view === 'detail' && pageData.quote) {
                    const shortContent = pageData.quote.content.substring(0, 100) + '...';
                    config.title = `${shortContent} - ${I18n.t('site.title')}`;
                    const authorName = pageData.quote.author && pageData.quote.author.name ? pageData.quote.author.name : '';
                    config.description = `${pageData.quote.content} - ${authorName}`;
                    config.keywords = `名言,${authorName},${config.keywords}`;
                    config.ogType = 'article';
                }
                break;
                
            case 'search':
                if (route.view === 'results' && pageData.query) {
                    config.title = `"${pageData.query}" - ${I18n.t('page.search.title')} - ${I18n.t('site.title')}`;
                    config.description = `搜索"${pageData.query}"的结果，找到相关的名言警句`;
                    config.keywords = `${pageData.query},搜索,${config.keywords}`;
                } else {
                    config.title = I18n.t('page.search.title') + ' - ' + I18n.t('site.title');
                    config.description = I18n.t('page.search.description');
                }
                break;
        }
        
        // 设置语言相关配置
        config.language = language;
        config.ogLocale = language === 'en' ? 'en_US' : 'zh_CN';
        config.ogLocaleAlternate = language === 'en' ? ['zh_CN'] : ['en_US'];
        
        return config;
    },
    
    /**
     * 设置事件监听
     */
    setupEventListeners: function() {
        // 监听路由变化
        document.addEventListener('routeChanged', () => {
            setTimeout(() => {
                const pageData = window.currentPageData || {};
                const metaConfig = this.generatePageMeta(pageData);
                this.setPageMeta(metaConfig);
            }, 100);
        });
        
        // 监听语言变化
        document.addEventListener('languageChanged', () => {
            setTimeout(() => {
                const pageData = window.currentPageData || {};
                const metaConfig = this.generatePageMeta(pageData);
                this.setPageMeta(metaConfig);
            }, 100);
        });
    },
    
    /**
     * 手动更新页面meta
     * @param {Object} pageData - 页面数据
     */
    updatePageMeta: function(pageData = {}) {
        window.currentPageData = pageData;
        const metaConfig = this.generatePageMeta(pageData);
        this.setPageMeta(metaConfig);
    }
};

// 自动初始化
if (typeof document !== 'undefined') {
    document.addEventListener('DOMContentLoaded', () => {
        MetaTags.init();
    });
}
