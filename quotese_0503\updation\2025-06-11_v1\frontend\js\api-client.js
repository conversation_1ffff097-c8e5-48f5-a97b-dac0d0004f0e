/**
 * API客户端
 * 负责与后端API通信
 */
class ApiClient {
    /**
     * 创建API客户端实例
     * @param {string} endpoint - API端点URL
     * @param {boolean} useMockData - 是否使用模拟数据
     */
    constructor(endpoint = 'https://api.quotese.com/graphql', useMockData = false) {
        this.endpoint = endpoint;
        this.useMockData = useMockData;
        this.mockData = {
            quotes: [
                {
                    id: 1,
                    content: "Life is like a box of chocolates. You never know what you're gonna get.",
                    author: { id: 1, name: "<PERSON> Gump" },
                    categories: [{ id: 1, name: "Movies" }, { id: 2, name: "Inspiration" }],
                    sources: [{ id: 1, name: "<PERSON>" }]
                },
                {
                    id: 2,
                    content: "Get busy living, or get busy dying.",
                    author: { id: 2, name: "<PERSON>" },
                    categories: [{ id: 1, name: "Movies" }, { id: 3, name: "Life" }],
                    sources: [{ id: 2, name: "The Shawshank Redemption" }]
                },
                {
                    id: 3,
                    content: "Hope is a good thing, maybe the best of things, and no good thing ever dies.",
                    author: { id: 2, name: "<PERSON>" },
                    categories: [{ id: 1, name: "<PERSON>" }, { id: 4, name: "<PERSON>" }],
                    sources: [{ id: 2, name: "The <PERSON>shank Redemption" }]
                }
            ],
            authors: [
                { id: 1, name: "<PERSON> Gump", quotes_count: 1 },
                { id: 2, name: "<PERSON> Dufresne", quotes_count: 2 }
            ],
            categories: [
                { id: 1, name: "Movies", quotes_count: 3 },
                { id: 2, name: "Inspiration", quotes_count: 1 },
                { id: 3, name: "Life", quotes_count: 1 },
                { id: 4, name: "Hope", quotes_count: 1 }
            ],
            sources: [
                { id: 1, name: "Forrest Gump", quotes_count: 1 },
                { id: 2, name: "The Shawshank Redemption", quotes_count: 2 }
            ],
            // 2025-06-11: 修改部分开始
            // 功能说明: 添加了搜索结果的模拟数据
            searchResults: {
                "hope": [
                    {
                        id: 3,
                        content: "Hope is a good thing, maybe the best of things, and no good thing ever dies.",
                        author: { id: 2, name: "Andy Dufresne" },
                        categories: [{ id: 1, name: "Movies" }, { id: 4, name: "Hope" }],
                        sources: [{ id: 2, name: "The Shawshank Redemption" }]
                    }
                ],
                "life": [
                    {
                        id: 1,
                        content: "Life is like a box of chocolates. You never know what you're gonna get.",
                        author: { id: 1, name: "Forrest Gump" },
                        categories: [{ id: 1, name: "Movies" }, { id: 2, name: "Inspiration" }],
                        sources: [{ id: 1, name: "Forrest Gump" }]
                    }
                ]
            }
            // 2025-06-11: 修改部分结束
        };
    }

    /**
     * 获取名言列表
     * @param {number} limit - 结果数量限制
     * @param {number} offset - 结果偏移量
     * @returns {Promise<Array>} - 名言列表
     */
    async getQuotes(limit = 10, offset = 0) {
        if (this.useMockData) {
            return Promise.resolve(this.mockData.quotes.slice(offset, offset + limit));
        }
        
        try {
            const query = `
                query {
                    quotes(first: ${limit}, skip: ${offset}) {
                        id
                        content
                        author {
                            id
                            name
                        }
                        categories {
                            id
                            name
                        }
                        sources {
                            id
                            name
                        }
                    }
                }
            `;
            
            const response = await this._graphqlRequest(query);
            return response.data.quotes;
        } catch (error) {
            console.error('Error fetching quotes:', error);
            throw error;
        }
    }

    /**
     * 获取名言详情
     * @param {number} id - 名言ID
     * @returns {Promise<Object>} - 名言详情
     */
    async getQuote(id) {
        if (this.useMockData) {
            const quote = this.mockData.quotes.find(q => q.id == id);
            return Promise.resolve(quote || null);
        }
        
        try {
            const query = `
                query {
                    quote(id: ${id}) {
                        id
                        content
                        author {
                            id
                            name
                        }
                        categories {
                            id
                            name
                        }
                        sources {
                            id
                            name
                        }
                        createdAt
                        updatedAt
                    }
                }
            `;
            
            const response = await this._graphqlRequest(query);
            return response.data.quote;
        } catch (error) {
            console.error(`Error fetching quote with ID ${id}:`, error);
            throw error;
        }
    }

    /**
     * 获取作者列表
     * @param {number} limit - 结果数量限制
     * @param {number} offset - 结果偏移量
     * @returns {Promise<Array>} - 作者列表
     */
    async getAuthors(limit = 10, offset = 0) {
        if (this.useMockData) {
            return Promise.resolve(this.mockData.authors.slice(offset, offset + limit));
        }
        
        try {
            const query = `
                query {
                    authors(first: ${limit}, skip: ${offset}) {
                        id
                        name
                        quotesCount
                    }
                }
            `;
            
            const response = await this._graphqlRequest(query);
            return response.data.authors.map(author => ({
                ...author,
                quotes_count: author.quotesCount
            }));
        } catch (error) {
            console.error('Error fetching authors:', error);
            throw error;
        }
    }

    /**
     * 获取作者详情
     * @param {number} id - 作者ID
     * @returns {Promise<Object>} - 作者详情
     */
    async getAuthor(id) {
        if (this.useMockData) {
            const author = this.mockData.authors.find(a => a.id == id);
            return Promise.resolve(author || null);
        }
        
        try {
            const query = `
                query {
                    author(id: ${id}) {
                        id
                        name
                        quotesCount
                        quotes {
                            id
                            content
                            categories {
                                id
                                name
                            }
                            sources {
                                id
                                name
                            }
                        }
                    }
                }
            `;
            
            const response = await this._graphqlRequest(query);
            const author = response.data.author;
            return {
                ...author,
                quotes_count: author.quotesCount
            };
        } catch (error) {
            console.error(`Error fetching author with ID ${id}:`, error);
            throw error;
        }
    }

    /**
     * 获取类别列表
     * @param {number} limit - 结果数量限制
     * @param {number} offset - 结果偏移量
     * @returns {Promise<Array>} - 类别列表
     */
    async getCategories(limit = 10, offset = 0) {
        if (this.useMockData) {
            return Promise.resolve(this.mockData.categories.slice(offset, offset + limit));
        }
        
        try {
            const query = `
                query {
                    categories(first: ${limit}, skip: ${offset}) {
                        id
                        name
                        quotesCount
                    }
                }
            `;
            
            const response = await this._graphqlRequest(query);
            return response.data.categories.map(category => ({
                ...category,
                quotes_count: category.quotesCount
            }));
        } catch (error) {
            console.error('Error fetching categories:', error);
            throw error;
        }
    }

    /**
     * 获取类别详情
     * @param {number} id - 类别ID
     * @returns {Promise<Object>} - 类别详情
     */
    async getCategory(id) {
        if (this.useMockData) {
            const category = this.mockData.categories.find(c => c.id == id);
            return Promise.resolve(category || null);
        }
        
        try {
            const query = `
                query {
                    category(id: ${id}) {
                        id
                        name
                        quotesCount
                        quotes {
                            id
                            content
                            author {
                                id
                                name
                            }
                            sources {
                                id
                                name
                            }
                        }
                    }
                }
            `;
            
            const response = await this._graphqlRequest(query);
            const category = response.data.category;
            return {
                ...category,
                quotes_count: category.quotesCount
            };
        } catch (error) {
            console.error(`Error fetching category with ID ${id}:`, error);
            throw error;
        }
    }

    /**
     * 获取来源列表
     * @param {number} limit - 结果数量限制
     * @param {number} offset - 结果偏移量
     * @returns {Promise<Array>} - 来源列表
     */
    async getSources(limit = 10, offset = 0) {
        if (this.useMockData) {
            return Promise.resolve(this.mockData.sources.slice(offset, offset + limit));
        }
        
        try {
            const query = `
                query {
                    sources(first: ${limit}, skip: ${offset}) {
                        id
                        name
                        quotesCount
                    }
                }
            `;
            
            const response = await this._graphqlRequest(query);
            return response.data.sources.map(source => ({
                ...source,
                quotes_count: source.quotesCount
            }));
        } catch (error) {
            console.error('Error fetching sources:', error);
            throw error;
        }
    }

    /**
     * 获取来源详情
     * @param {number} id - 来源ID
     * @returns {Promise<Object>} - 来源详情
     */
    async getSource(id) {
        if (this.useMockData) {
            const source = this.mockData.sources.find(s => s.id == id);
            return Promise.resolve(source || null);
        }
        
        try {
            const query = `
                query {
                    source(id: ${id}) {
                        id
                        name
                        quotesCount
                        quotes {
                            id
                            content
                            author {
                                id
                                name
                            }
                            categories {
                                id
                                name
                            }
                        }
                    }
                }
            `;
            
            const response = await this._graphqlRequest(query);
            const source = response.data.source;
            return {
                ...source,
                quotes_count: source.quotesCount
            };
        } catch (error) {
            console.error(`Error fetching source with ID ${id}:`, error);
            throw error;
        }
    }

    /**
     * 获取统计信息
     * @returns {Promise<Object>} - 统计信息
     */
    async getStats() {
        if (this.useMockData) {
            return Promise.resolve({
                quotes_count: this.mockData.quotes.length,
                authors_count: this.mockData.authors.length,
                categories_count: this.mockData.categories.length,
                sources_count: this.mockData.sources.length
            });
        }
        
        try {
            const query = `
                query {
                    quotesCount
                    authorsCount
                    categoriesCount
                    sourcesCount
                }
            `;
            
            const response = await this._graphqlRequest(query);
            return {
                quotes_count: response.data.quotesCount,
                authors_count: response.data.authorsCount,
                categories_count: response.data.categoriesCount,
                sources_count: response.data.sourcesCount
            };
        } catch (error) {
            console.error('Error fetching stats:', error);
            throw error;
        }
    }

    /**
     * 2025-06-11: 修改部分开始
     * 功能说明: 添加了搜索名言的方法
     * 
     * 搜索名言
     * @param {string} query - 搜索关键词
     * @returns {Promise<Array>} - 搜索结果
     */
    async searchQuotes(query) {
        if (!query || query.trim() === '') {
            return Promise.resolve([]);
        }
        
        if (this.useMockData) {
            // 使用模拟数据中的搜索结果
            const normalizedQuery = query.toLowerCase().trim();
            const results = this.mockData.searchResults[normalizedQuery];
            return Promise.resolve(results || []);
        }
        
        try {
            const graphqlQuery = `
                query {
                    quotes(search: "${query}", first: 20) {
                        id
                        content
                        author {
                            id
                            name
                        }
                        categories {
                            id
                            name
                        }
                        sources {
                            id
                            name
                        }
                    }
                }
            `;
            
            const response = await this._graphqlRequest(graphqlQuery);
            return response.data.quotes;
        } catch (error) {
            console.error(`Error searching quotes with query "${query}":`, error);
            throw error;
        }
    }
    // 2025-06-11: 修改部分结束

    /**
     * 发送GraphQL请求
     * @param {string} query - GraphQL查询
     * @param {Object} variables - 查询变量
     * @returns {Promise<Object>} - 响应数据
     * @private
     */
    async _graphqlRequest(query, variables = {}) {
        try {
            const response = await fetch(this.endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                },
                body: JSON.stringify({
                    query,
                    variables
                })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('GraphQL request error:', error);
            throw error;
        }
    }
}

// 如果在浏览器环境中，将ApiClient添加到window对象
if (typeof window !== 'undefined') {
    window.ApiClient = ApiClient;
}

// 如果支持模块导出，导出ApiClient
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ApiClient;
}

