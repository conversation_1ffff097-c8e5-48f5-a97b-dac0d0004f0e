# Nginx前端配置 - SEO优化版本 2025-01-16
# 支持RESTful URL、多语言、SEO优化

server {
    listen 80;
    server_name quotese.com www.quotese.com;
    
    # 重定向到HTTPS（生产环境启用）
    # return 301 https://$server_name$request_uri;
    
    root /usr/share/nginx/html;
    index index.html;
    
    # 字符编码
    charset utf-8;
    
    # ==========================================
    # 静态文件缓存优化
    # ==========================================
    
    # 长期缓存静态资源
    location ~* \.(css|js|jpg|jpeg|png|gif|ico|svg|woff|woff2|ttf|eot|webp)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary "Accept-Encoding";
        
        # 启用gzip压缩
        gzip_static on;
    }
    
    # HTML文件不缓存
    location ~* \.html$ {
        expires -1;
        add_header Cache-Control "no-store, no-cache, must-revalidate, proxy-revalidate";
        add_header Vary "Accept-Encoding";
    }
    
    # ==========================================
    # SEO相关文件
    # ==========================================
    
    # 网站地图和robots.txt
    location = /sitemap.xml {
        expires 1d;
        add_header Content-Type "application/xml";
    }
    
    location = /robots.txt {
        expires 1d;
        add_header Content-Type "text/plain";
    }
    
    # ==========================================
    # 向后兼容性 - 301重定向
    # ==========================================
    
    # 重定向旧的作者页面
    location ~ ^/author\.html$ {
        if ($args ~ "^id=([0-9]+)&name=([^&]+)$") {
            return 301 /authors/$arg_name/;
        }
        return 301 /authors/;
    }
    
    # 重定向旧的类别页面
    location ~ ^/category\.html$ {
        if ($args ~ "^id=([0-9]+)&name=([^&]+)$") {
            return 301 /categories/$arg_name/;
        }
        return 301 /categories/;
    }
    
    # 重定向旧的来源页面
    location ~ ^/source\.html$ {
        if ($args ~ "^id=([0-9]+)&name=([^&]+)$") {
            return 301 /sources/$arg_name/;
        }
        return 301 /sources/;
    }
    
    # 重定向旧的名言页面
    location ~ ^/quote\.html$ {
        if ($args ~ "^id=([0-9]+)$") {
            return 301 /quotes/$arg_id/;
        }
        return 301 /;
    }
    
    # 重定向旧的搜索页面
    location ~ ^/search\.html$ {
        if ($args ~ "^q=([^&]+)$") {
            return 301 /search/$arg_q/;
        }
        return 301 /search/;
    }
    
    # ==========================================
    # 新的RESTful URL路由
    # ==========================================
    
    # 首页
    location = / {
        try_files /index.html =404;
    }
    
    location = /home/<USER>
        return 301 /;
    }
    
    # 英文首页
    location ~ ^/en/?$ {
        try_files /index.html?lang=en =404;
    }
    
    # 作者相关页面
    location ~ ^/authors/?$ {
        try_files /authors.html =404;
    }
    
    location ~ ^/authors/([^/]+)/?$ {
        try_files /authors.html?slug=$1 =404;
    }
    
    location ~ ^/authors/([^/]+)/quotes/?$ {
        try_files /authors.html?slug=$1&view=quotes =404;
    }
    
    # 英文作者页面
    location ~ ^/en/authors/?$ {
        try_files /authors.html?lang=en =404;
    }
    
    location ~ ^/en/authors/([^/]+)/?$ {
        try_files /authors.html?slug=$1&lang=en =404;
    }
    
    location ~ ^/en/authors/([^/]+)/quotes/?$ {
        try_files /authors.html?slug=$1&view=quotes&lang=en =404;
    }
    
    # 类别相关页面
    location ~ ^/categories/?$ {
        try_files /categories.html =404;
    }
    
    location ~ ^/categories/([^/]+)/?$ {
        try_files /categories.html?slug=$1 =404;
    }
    
    location ~ ^/categories/([^/]+)/quotes/?$ {
        try_files /categories.html?slug=$1&view=quotes =404;
    }
    
    # 英文类别页面
    location ~ ^/en/categories/?$ {
        try_files /categories.html?lang=en =404;
    }
    
    location ~ ^/en/categories/([^/]+)/?$ {
        try_files /categories.html?slug=$1&lang=en =404;
    }
    
    # 来源相关页面
    location ~ ^/sources/?$ {
        try_files /sources.html =404;
    }
    
    location ~ ^/sources/([^/]+)/?$ {
        try_files /sources.html?slug=$1 =404;
    }
    
    location ~ ^/sources/([^/]+)/quotes/?$ {
        try_files /sources.html?slug=$1&view=quotes =404;
    }
    
    # 英文来源页面
    location ~ ^/en/sources/?$ {
        try_files /sources.html?lang=en =404;
    }
    
    location ~ ^/en/sources/([^/]+)/?$ {
        try_files /sources.html?slug=$1&lang=en =404;
    }
    
    # 名言详情页面
    location ~ ^/quotes/([0-9]+)/?$ {
        try_files /quotes.html?id=$1 =404;
    }
    
    location ~ ^/en/quotes/([0-9]+)/?$ {
        try_files /quotes.html?id=$1&lang=en =404;
    }
    
    # 搜索页面
    location ~ ^/search/?$ {
        try_files /search.html =404;
    }
    
    location ~ ^/search/([^/]+)/?$ {
        try_files /search.html?q=$1 =404;
    }
    
    location ~ ^/en/search/?$ {
        try_files /search.html?lang=en =404;
    }
    
    location ~ ^/en/search/([^/]+)/?$ {
        try_files /search.html?q=$1&lang=en =404;
    }
    
    # ==========================================
    # 错误处理
    # ==========================================
    
    # 404错误页面
    error_page 404 /404.html;
    
    # 50x错误页面
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
    
    # ==========================================
    # 安全设置
    # ==========================================
    
    # 隐藏Nginx版本
    server_tokens off;
    
    # 防止访问敏感文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ ~$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # ==========================================
    # 性能优化
    # ==========================================
    
    # 启用gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;
    
    # 默认处理
    location / {
        try_files $uri $uri/ /404.html;
    }
}
