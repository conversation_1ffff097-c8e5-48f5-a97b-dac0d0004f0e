/**
 * 搜索页面样式
 * 创建日期: 2025-06-13
 * 说明: 为搜索功能页面提供专用样式，包括搜索框、结果展示、分页等组件的样式定义
 */

/* 搜索页面主容器 */
.search-page {
    min-height: 100vh;
    padding-top: 2rem;
    padding-bottom: 2rem;
}

/* 搜索头部区域 */
/* 2025-06-13: 搜索页面头部样式，包含渐变背景和圆角设计 */
.search-header {
    background: linear-gradient(135deg, #fffbeb 0%, #fff8c4 25%, #fffef2 75%, #ffffff 100%);
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.dark-mode .search-header {
    background: linear-gradient(135deg, #1f2937 0%, #2d3748 25%, #1a202c 75%, #111827 100%);
    border-color: #374151;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
}

/* 搜索框样式 */
/* 2025-06-13: 响应式搜索输入框设计，支持焦点状态和主题切换 */
.search-input-container {
    position: relative;
    max-width: 600px;
    margin: 0 auto;
}

.search-input {
    width: 100%;
    padding: 1rem 3rem 1rem 1.5rem;
    border: 2px solid #e5e7eb;
    border-radius: 0.75rem;
    font-size: 1.125rem;
    background-color: #ffffff;
    color: #1f2937;
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: #FFD300;
    box-shadow: 0 0 0 3px rgba(255, 211, 0, 0.1);
}

.dark-mode .search-input {
    background-color: #374151;
    border-color: #4b5563;
    color: #f9fafb;
}

.dark-mode .search-input:focus {
    border-color: #FFD300;
    box-shadow: 0 0 0 3px rgba(255, 211, 0, 0.2);
}

.search-button {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6b7280;
    font-size: 1.25rem;
    cursor: pointer;
    transition: color 0.3s ease;
}

.search-button:hover {
    color: #FFD300;
}

/* 搜索结果区域 */
/* 2025-06-13: 搜索结果展示区域样式 */
.search-results {
    margin-top: 2rem;
}

.search-results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f3f4f6;
}

.dark-mode .search-results-header {
    border-bottom-color: #374151;
}

.search-results-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
}

.dark-mode .search-results-title {
    color: #f9fafb;
}

.search-results-count {
    color: #6b7280;
    font-size: 0.875rem;
}

.dark-mode .search-results-count {
    color: #9ca3af;
}

/* 搜索结果项 */
/* 2025-06-13: 搜索结果卡片样式，包含悬停效果和点击交互 */
.search-result-item {
    background-color: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.search-result-item:hover {
    border-color: #FFD300;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.dark-mode .search-result-item {
    background-color: #1f2937;
    border-color: #374151;
}

.dark-mode .search-result-item:hover {
    border-color: #FFD300;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
}

.search-result-quote {
    font-size: 1.125rem;
    line-height: 1.6;
    color: #1f2937;
    margin-bottom: 1rem;
    font-style: italic;
}

.dark-mode .search-result-quote {
    color: #f9fafb;
}

.search-result-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: center;
    font-size: 0.875rem;
    color: #6b7280;
}

.dark-mode .search-result-meta {
    color: #9ca3af;
}

.search-result-author {
    font-weight: 600;
    color: #FFD300;
}

.search-result-categories,
.search-result-sources {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.search-result-tag {
    background-color: #f3f4f6;
    color: #374151;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.dark-mode .search-result-tag {
    background-color: #374151;
    color: #d1d5db;
}

/* 无结果状态 */
/* 2025-06-13: 无搜索结果时的友好提示界面 */
.no-results {
    text-align: center;
    padding: 3rem 1rem;
    color: #6b7280;
}

.dark-mode .no-results {
    color: #9ca3af;
}

.no-results-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.no-results-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #374151;
}

.dark-mode .no-results-title {
    color: #d1d5db;
}

.no-results-text {
    font-size: 1rem;
    margin-bottom: 1.5rem;
}

.no-results-suggestions {
    background-color: #f9fafb;
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-top: 2rem;
    text-align: left;
}

.dark-mode .no-results-suggestions {
    background-color: #374151;
}

.no-results-suggestions h4 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1f2937;
}

.dark-mode .no-results-suggestions h4 {
    color: #f9fafb;
}

.no-results-suggestions ul {
    list-style-type: disc;
    padding-left: 1.5rem;
    color: #6b7280;
}

.dark-mode .no-results-suggestions ul {
    color: #9ca3af;
}

.no-results-suggestions li {
    margin-bottom: 0.5rem;
}

/* 加载状态 */
/* 2025-06-13: 搜索加载状态指示器 */
.search-loading {
    text-align: center;
    padding: 3rem 1rem;
}

.search-loading-spinner {
    display: inline-block;
    width: 2rem;
    height: 2rem;
    border: 3px solid #f3f4f6;
    border-top: 3px solid #FFD300;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

.dark-mode .search-loading-spinner {
    border-color: #374151;
    border-top-color: #FFD300;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
/* 2025-06-13: 移动设备适配样式 */
@media (max-width: 768px) {
    .search-header {
        padding: 1.5rem;
    }
    
    .search-results-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .search-result-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}

/* 搜索建议高亮 */
/* 2025-06-13: 搜索关键词高亮显示样式 */
.search-highlight {
    background-color: rgba(255, 211, 0, 0.3);
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-weight: 600;
}

.dark-mode .search-highlight {
    background-color: rgba(255, 211, 0, 0.4);
}
