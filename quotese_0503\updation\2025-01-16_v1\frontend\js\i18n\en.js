/**
 * English Language Pack - 2025-01-16
 */

window.enMessages = {
    // Navigation menu
    'nav.home': 'Home',
    'nav.authors': 'Authors',
    'nav.categories': 'Categories',
    'nav.sources': 'Sources',
    'nav.search': 'Search',
    'nav.about': 'About',
    'nav.contact': 'Contact',
    
    // Page titles
    'page.home.title': 'Famous Quotes - Wisdom for Life',
    'page.home.description': 'Discover inspiring quotes from around the world to guide your life with wisdom',
    'page.authors.title': 'Famous Authors',
    'page.authors.description': 'Explore wisdom from renowned thinkers, writers, and philosophers throughout history',
    'page.categories.title': 'Quote Categories',
    'page.categories.description': 'Browse quotes by theme to quickly find the wisdom you seek',
    'page.sources.title': 'Quote Sources',
    'page.sources.description': 'Discover the origins and background of famous quotes',
    'page.search.title': 'Search Quotes',
    'page.search.description': 'Search for quotes, authors, or topics that interest you',
    
    // Site title suffix
    'site.title': 'Famous Quotes',
    'site.description': 'Discover inspiring quotes from around the world, spreading wisdom and philosophy',
    
    // Buttons and actions
    'btn.search': 'Search',
    'btn.more': 'More',
    'btn.back': 'Back',
    'btn.share': 'Share',
    'btn.copy': 'Copy',
    'btn.like': 'Like',
    'btn.favorite': 'Favorite',
    'btn.download': 'Download',
    'btn.print': 'Print',
    'btn.close': 'Close',
    'btn.cancel': 'Cancel',
    'btn.confirm': 'Confirm',
    'btn.submit': 'Submit',
    'btn.reset': 'Reset',
    'btn.edit': 'Edit',
    'btn.delete': 'Delete',
    'btn.save': 'Save',
    'btn.load_more': 'Load More',
    
    // Pagination
    'pagination.prev': 'Previous',
    'pagination.next': 'Next',
    'pagination.first': 'First',
    'pagination.last': 'Last',
    'pagination.page': 'Page {page}',
    'pagination.total': 'Total {total} items',
    'pagination.per_page': '{count} per page',
    'pagination.goto': 'Go to',
    'pagination.of': 'of {total} pages',
    
    // Search related
    'search.placeholder': 'Search quotes, authors, categories...',
    'search.no_results': 'No results found',
    'search.results_count': 'Found {count} results',
    'search.results_for': 'Search results for "{query}"',
    'search.suggestions': 'Search suggestions',
    'search.recent': 'Recent searches',
    'search.popular': 'Popular searches',
    'search.clear': 'Clear search',
    'search.advanced': 'Advanced search',
    
    // Filter and sort
    'filter.all': 'All',
    'filter.by_author': 'Filter by author',
    'filter.by_category': 'Filter by category',
    'filter.by_source': 'Filter by source',
    'filter.by_language': 'Filter by language',
    'filter.clear': 'Clear filters',
    'sort.default': 'Default order',
    'sort.newest': 'Newest first',
    'sort.oldest': 'Oldest first',
    'sort.popular': 'Most popular',
    'sort.alphabetical': 'Alphabetical',
    'sort.random': 'Random order',
    
    // Content related
    'content.author': 'Author',
    'content.category': 'Category',
    'content.source': 'Source',
    'content.quote': 'Quote',
    'content.quotes': 'Quotes',
    'content.description': 'Description',
    'content.tags': 'Tags',
    'content.language': 'Language',
    'content.date': 'Date',
    'content.views': 'Views',
    'content.likes': 'Likes',
    'content.shares': 'Shares',
    'content.related': 'Related content',
    'content.similar': 'Similar content',
    'content.recommended': 'Recommended content',
    
    // Author related
    'author.profile': 'Author profile',
    'author.biography': 'Biography',
    'author.birth_date': 'Birth date',
    'author.death_date': 'Death date',
    'author.nationality': 'Nationality',
    'author.occupation': 'Occupation',
    'author.famous_works': 'Famous works',
    'author.quote_count': 'Number of quotes',
    'author.view_all_quotes': 'View all quotes',
    
    // Category related
    'category.description': 'Category description',
    'category.quote_count': 'Number of quotes',
    'category.popular_quotes': 'Popular quotes',
    'category.recent_quotes': 'Recent quotes',
    'category.view_all': 'View all',
    
    // Source related
    'source.description': 'Source description',
    'source.type': 'Source type',
    'source.publication_date': 'Publication date',
    'source.publisher': 'Publisher',
    'source.isbn': 'ISBN',
    'source.quote_count': 'Number of quotes',
    
    // Error messages
    'error.404': 'Page Not Found',
    'error.404.description': 'Sorry, the page you are looking for does not exist',
    'error.500': 'Server Error',
    'error.500.description': 'The server encountered an error, please try again later',
    'error.network': 'Network Connection Error',
    'error.network.description': 'Please check your network connection',
    'error.timeout': 'Request Timeout',
    'error.timeout.description': 'Request timed out, please try again later',
    'error.unknown': 'Unknown Error',
    'error.unknown.description': 'An unknown error occurred, please try again later',
    
    // Success messages
    'success.copied': 'Copied to clipboard',
    'success.shared': 'Shared successfully',
    'success.saved': 'Saved successfully',
    'success.liked': 'Liked successfully',
    'success.favorited': 'Added to favorites',
    
    // Status messages
    'status.loading': 'Loading...',
    'status.searching': 'Searching...',
    'status.saving': 'Saving...',
    'status.processing': 'Processing...',
    'status.uploading': 'Uploading...',
    'status.downloading': 'Downloading...',
    
    // Time related
    'time.just_now': 'Just now',
    'time.minutes_ago': '{count} minutes ago',
    'time.hours_ago': '{count} hours ago',
    'time.days_ago': '{count} days ago',
    'time.weeks_ago': '{count} weeks ago',
    'time.months_ago': '{count} months ago',
    'time.years_ago': '{count} years ago',
    
    // Quantity units
    'unit.count': '',
    'unit.item': 'item',
    'unit.page': 'page',
    'unit.view': 'view',
    'unit.like': 'like',
    'unit.share': 'share',
    
    // Language selection
    'language.chinese': '中文',
    'language.english': 'English',
    'language.switch_to': 'Switch to {language}',
    
    // Breadcrumb navigation
    'breadcrumb.home': 'Home',
    'breadcrumb.authors': 'Authors',
    'breadcrumb.categories': 'Categories',
    'breadcrumb.sources': 'Sources',
    'breadcrumb.search': 'Search',
    'breadcrumb.quotes': 'Quotes',
    
    // Social sharing
    'share.title': 'Share this quote',
    'share.facebook': 'Share on Facebook',
    'share.twitter': 'Share on Twitter',
    'share.weibo': 'Share on Weibo',
    'share.wechat': 'Share on WeChat',
    'share.qq': 'Share on QQ',
    'share.link': 'Copy link',
    'share.email': 'Share via email',
    
    // Form related
    'form.required': 'Required',
    'form.optional': 'Optional',
    'form.invalid': 'Invalid format',
    'form.too_short': 'Too short',
    'form.too_long': 'Too long',
    'form.email_invalid': 'Invalid email format',
    'form.url_invalid': 'Invalid URL format',
    
    // Other common words
    'common.yes': 'Yes',
    'common.no': 'No',
    'common.ok': 'OK',
    'common.or': 'or',
    'common.and': 'and',
    'common.all': 'All',
    'common.none': 'None',
    'common.other': 'Other',
    'common.unknown': 'Unknown',
    'common.default': 'Default',
    'common.custom': 'Custom',
    'common.settings': 'Settings',
    'common.help': 'Help',
    'common.about': 'About',
    'common.contact': 'Contact Us',
    'common.privacy': 'Privacy Policy',
    'common.terms': 'Terms of Service',
    'common.copyright': 'All rights reserved',
    
    // Mobile related
    'mobile.menu': 'Menu',
    'mobile.close_menu': 'Close menu',
    'mobile.back': 'Back',
    'mobile.scroll_to_top': 'Scroll to top',
    
    // Accessibility related
    'a11y.skip_to_content': 'Skip to main content',
    'a11y.open_menu': 'Open menu',
    'a11y.close_menu': 'Close menu',
    'a11y.search_button': 'Search button',
    'a11y.language_selector': 'Language selector',
    'a11y.page_navigation': 'Page navigation',
    'a11y.main_content': 'Main content',
    'a11y.sidebar': 'Sidebar',
    'a11y.footer': 'Footer'
};
