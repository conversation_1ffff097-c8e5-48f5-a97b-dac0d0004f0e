<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search Quotes | Famous Quotes Collection - quotese.com</title>
    <meta name="description" content="Search through our vast collection of famous quotes, inspirational sayings, and wisdom from renowned figures worldwide.">
    <meta name="keywords" content="search quotes, find quotes, famous quotes search, inspirational quotes search, quote finder">
    <link rel="canonical" href="https://quotese.com/search.html">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Search Quotes | Famous Quotes Collection - quotese.com">
    <meta property="og:description" content="Search through our vast collection of famous quotes, inspirational sayings, and wisdom from renowned figures worldwide.">
    <meta property="og:image" content="https://quotese.com/images/og-image-search.jpg">
    <meta property="og:url" content="https://quotese.com/search.html">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="quotese.com">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@quotesecom">
    <meta name="twitter:title" content="Search Quotes | Famous Quotes Collection">
    <meta name="twitter:description" content="Search through our vast collection of famous quotes, inspirational sayings, and wisdom from renowned figures worldwide.">
    <meta name="twitter:image" content="https://quotese.com/images/og-image-search.jpg">

    <!-- Tailwind CSS -->
    <link href="https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif:wght@400;500;600;700&family=Noto+Sans:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="css/variables.css" rel="stylesheet">
    <link href="css/styles.css" rel="stylesheet">
    <link href="css/buttons.css" rel="stylesheet">
    <link href="css/animations.css" rel="stylesheet">
    <link href="css/responsive.css" rel="stylesheet">
    <!-- 2025-01-27: 新增搜索页面专用样式文件 -->
    <link href="css/pages/search.css" rel="stylesheet">

    <!-- Bing Clarity -->
    <script type="text/javascript">
        (function(c,l,a,r,i,t,y){
            c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
            t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
            y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
        })(window, document, "clarity", "script", "rxa7t53bmr");
    </script>
    <!-- Google Analytics -->
    <script src="js/analytics.js"></script>
</head>
<body class="light-mode">
    <!-- 导航栏 (将由组件加载器加载) -->
    <header id="navigation-container" role="banner"></header>

    <!-- 主要内容 -->
    <!-- 2025-01-27: 新增搜索页面主要内容区域 -->
    <main class="container mx-auto px-4 py-8 search-page" role="main">
        <!-- 搜索头部 -->
        <section class="search-header" aria-labelledby="search-heading">
            <h1 id="search-heading" class="text-3xl md:text-4xl font-bold text-center mb-6">
                Search <span style="color: #FFD300;">Quotes</span>
            </h1>
            <p class="text-lg text-gray-600 dark:text-gray-300 text-center mb-8 max-w-2xl mx-auto">
                Discover wisdom from great minds throughout history. Search by content, author, category, or source.
            </p>
            
            <!-- 搜索框 -->
            <div class="search-input-container">
                <input 
                    type="text" 
                    id="search-input" 
                    class="search-input" 
                    placeholder="Search for quotes, authors, categories..."
                    autocomplete="off"
                    aria-label="Search quotes"
                >
                <button id="search-button" class="search-button" aria-label="Search">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </section>

        <!-- 面包屑导航 -->
        <nav id="breadcrumb-container" aria-label="Breadcrumb"></nav>

        <!-- 搜索结果区域 -->
        <section class="search-results" aria-live="polite">
            <!-- 搜索结果头部 -->
            <div id="search-results-header" class="search-results-header" style="display: none;">
                <h2 id="search-results-title" class="search-results-title">搜索结果</h2>
                <span id="search-results-count" class="search-results-count">找到 0 条结果</span>
            </div>

            <!-- 搜索结果容器 -->
            <div id="search-results-container" class="space-y-4">
                <!-- 搜索结果将在这里动态加载 -->
            </div>
        </section>

        <!-- 分页 -->
        <div id="pagination-container" class="mt-8">
            <!-- 分页组件将在这里加载 -->
        </div>
    </main>

    <!-- Footer (will be loaded by component loader) -->
    <footer id="footer-container" role="contentinfo"></footer>

    <!-- JavaScript -->
    <!-- Debug Script -->
    <script src="js/debug.js"></script>

    <!-- Component Loader -->
    <script src="js/component-loader.js"></script>

    <!-- Configuration -->
    <script src="js/config.js"></script>

    <!-- Mock Data -->
    <script src="js/mock-data.js"></script>

    <!-- API Client -->
    <script src="js/api-client.js"></script>

    <!-- Core Modules -->
    <script src="js/theme.js"></script>
    <script src="js/url-handler.js"></script>
    <script src="js/mobile-menu.js"></script>
    <script src="js/components/pagination.js"></script>
    <script src="js/components/quote-card.js"></script>
    <script src="js/components/breadcrumb.js"></script>
    <script src="js/social-meta.js"></script>

    <!-- Global Fix Script -->
    <script src="js/global-fix.js"></script>

    <!-- 2025-01-27: 新增搜索页面专用脚本 -->
    <!-- 搜索页面核心逻辑，包含搜索查询处理、API调用、结果展示、分页功能等 -->
    <script src="js/pages/search.js"></script>
</body>
</html>
