#!/bin/bash

# 快速替换.htaccess文件 - 修复404错误
# 2025-01-16

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置
PROJECT_ROOT="/var/www/quotese"
SCRIPT_DIR="$(dirname "$0")"
BACKUP_DIR="/var/www/quotese/backups/htaccess_$(date +%Y%m%d_%H%M%S)"

echo -e "${BLUE}=== QuoteSe .htaccess 快速替换 ===${NC}"
echo -e "${YELLOW}修复404错误 - 使用现有HTML文件名${NC}"
echo ""

# 检查权限
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}错误: 请使用sudo运行此脚本${NC}"
    echo "使用方法: sudo $0"
    exit 1
fi

# 检查项目目录
if [ ! -d "$PROJECT_ROOT" ]; then
    echo -e "${RED}错误: 项目目录不存在: $PROJECT_ROOT${NC}"
    exit 1
fi

# 检查修复后的.htaccess文件
if [ ! -f "$SCRIPT_DIR/frontend/.htaccess_fixed" ]; then
    echo -e "${RED}错误: 修复后的.htaccess文件不存在: $SCRIPT_DIR/frontend/.htaccess_fixed${NC}"
    exit 1
fi

# 创建备份目录
echo -e "${YELLOW}创建备份目录...${NC}"
mkdir -p "$BACKUP_DIR"

# 备份当前.htaccess
echo -e "${YELLOW}备份当前.htaccess文件...${NC}"
if [ -f "$PROJECT_ROOT/frontend/.htaccess" ]; then
    cp "$PROJECT_ROOT/frontend/.htaccess" "$BACKUP_DIR/.htaccess.backup"
    echo -e "${GREEN}备份完成: $BACKUP_DIR/.htaccess.backup${NC}"
else
    echo -e "${YELLOW}警告: 当前.htaccess文件不存在${NC}"
fi

# 替换.htaccess文件
echo -e "${YELLOW}替换.htaccess文件...${NC}"
cp "$SCRIPT_DIR/frontend/.htaccess_fixed" "$PROJECT_ROOT/frontend/.htaccess"
echo -e "${GREEN}.htaccess文件已替换${NC}"

# 设置正确的文件权限
echo -e "${YELLOW}设置文件权限...${NC}"
chown www-data:www-data "$PROJECT_ROOT/frontend/.htaccess"
chmod 644 "$PROJECT_ROOT/frontend/.htaccess"
echo -e "${GREEN}权限设置完成${NC}"

# 验证Apache配置
echo -e "${YELLOW}验证Apache配置...${NC}"
if command -v apache2ctl >/dev/null 2>&1; then
    if apache2ctl configtest; then
        echo -e "${GREEN}Apache配置验证通过${NC}"
    else
        echo -e "${RED}Apache配置验证失败，恢复备份...${NC}"
        if [ -f "$BACKUP_DIR/.htaccess.backup" ]; then
            cp "$BACKUP_DIR/.htaccess.backup" "$PROJECT_ROOT/frontend/.htaccess"
        fi
        exit 1
    fi
elif command -v httpd >/dev/null 2>&1; then
    if httpd -t; then
        echo -e "${GREEN}Apache配置验证通过${NC}"
    else
        echo -e "${RED}Apache配置验证失败，恢复备份...${NC}"
        if [ -f "$BACKUP_DIR/.htaccess.backup" ]; then
            cp "$BACKUP_DIR/.htaccess.backup" "$PROJECT_ROOT/frontend/.htaccess"
        fi
        exit 1
    fi
else
    echo -e "${YELLOW}警告: 无法找到Apache配置测试命令${NC}"
fi

# 重新加载Apache配置
echo -e "${YELLOW}重新加载Apache配置...${NC}"
if command -v systemctl >/dev/null 2>&1; then
    if systemctl is-active --quiet apache2; then
        systemctl reload apache2
        echo -e "${GREEN}Apache2配置已重新加载${NC}"
    elif systemctl is-active --quiet httpd; then
        systemctl reload httpd
        echo -e "${GREEN}HTTPD配置已重新加载${NC}"
    else
        echo -e "${YELLOW}警告: Apache服务未运行${NC}"
    fi
else
    echo -e "${YELLOW}警告: 无法重新加载Apache配置${NC}"
fi

# 显示修复内容
echo ""
echo -e "${BLUE}=== 修复内容 ===${NC}"
echo -e "${GREEN}✓ 修复了URL重写规则，使用现有HTML文件名:${NC}"
echo -e "  /categories/* → category.html"
echo -e "  /authors/* → author.html"
echo -e "  /sources/* → source.html"
echo -e "  /quotes/* → quote.html"
echo -e "  /search/* → search.html"
echo ""
echo -e "${GREEN}✓ 保持了SEO优化的URL结构${NC}"
echo -e "${GREEN}✓ 保持了多语言支持${NC}"
echo -e "${GREEN}✓ 保持了301重定向兼容性${NC}"
echo -e "${GREEN}✓ 添加了缓存和压缩优化${NC}"
echo -e "${GREEN}✓ 添加了安全头设置${NC}"

# 测试URL
echo ""
echo -e "${YELLOW}测试修复后的URL...${NC}"
if command -v curl >/dev/null 2>&1; then
    test_urls=(
        "http://localhost/categories/knowledge/"
        "http://localhost/authors/c-joybell-c/"
        "http://localhost/sources/alice-in-wonderland/"
    )
    
    for url in "${test_urls[@]}"; do
        echo -n "测试 $url ... "
        status=$(curl -s -o /dev/null -w "%{http_code}" "$url" 2>/dev/null || echo "000")
        if [ "$status" = "200" ]; then
            echo -e "${GREEN}✓ 200 OK${NC}"
        else
            echo -e "${RED}✗ HTTP $status${NC}"
        fi
    done
else
    echo -e "${YELLOW}curl命令不可用，请手动测试URL${NC}"
fi

# 生成替换报告
cat > "$BACKUP_DIR/replacement_report.txt" << EOF
.htaccess文件替换报告
===================

替换时间: $(date)
备份位置: $BACKUP_DIR/.htaccess.backup
新文件来源: $SCRIPT_DIR/frontend/.htaccess_fixed

修复内容:
1. 修复URL重写规则，使用现有HTML文件名
2. 保持SEO优化的RESTful URL结构
3. 保持多语言支持功能
4. 保持向后兼容的301重定向
5. 添加缓存和压缩优化
6. 添加安全头设置

修复的URL:
- /categories/knowledge/ → category.html?slug=knowledge
- /authors/c-joybell-c/ → author.html?slug=c-joybell-c
- /sources/alice-in-wonderland/ → source.html?slug=alice-in-wonderland

验证步骤:
1. 访问 https://quotese.com/categories/knowledge/
2. 访问 https://quotese.com/authors/c-joybell-c/
3. 访问 https://quotese.com/sources/alice-in-wonderland/

回滚方法:
sudo cp $BACKUP_DIR/.htaccess.backup $PROJECT_ROOT/frontend/.htaccess
sudo systemctl reload apache2
EOF

echo ""
echo -e "${GREEN}=== 替换完成 ===${NC}"
echo -e "${GREEN}✓ .htaccess文件已成功替换${NC}"
echo -e "${GREEN}✓ Apache配置已重新加载${NC}"
echo -e "${GREEN}✓ 备份文件: $BACKUP_DIR/.htaccess.backup${NC}"
echo -e "${GREEN}✓ 替换报告: $BACKUP_DIR/replacement_report.txt${NC}"
echo ""
echo -e "${BLUE}请立即验证以下URL是否正常访问:${NC}"
echo -e "${BLUE}1. https://quotese.com/categories/knowledge/${NC}"
echo -e "${BLUE}2. https://quotese.com/authors/c-joybell-c/${NC}"
echo -e "${BLUE}3. https://quotese.com/sources/alice-in-wonderland/${NC}"
echo ""
echo -e "${GREEN}修复成功！${NC}"
