/**
 * 作者页面脚本 - SEO优化版本 2025-01-16
 * 支持作者列表和作者详情的统一页面处理
 */

// 页面状态管理
const AuthorsPageState = {
    currentView: 'list', // 'list' | 'detail' | 'quotes'
    currentAuthor: null,
    currentPage: 1,
    pageSize: 20,
    totalPages: 1,
    totalAuthors: <AUTHORS>
    totalQuotes: 0,
    isLoading: false,
    searchQuery: '',
    sortBy: 'name',
    sortDirection: 'asc'
};

/**
 * 页面初始化
 */
async function initAuthorsPage() {
    try {
        // 解析URL参数
        parseUrlParameters();
        
        // 加载页面组件
        await loadPageComponents();
        
        // 根据URL参数决定显示内容
        await loadPageContent();
        
        // 设置事件监听
        setupEventListeners();
        
        // 更新页面元数据
        updatePageMetadata();
        
        console.log('作者页面初始化完成');
    } catch (error) {
        console.error('作者页面初始化失败:', error);
        showErrorMessage('页面加载失败，请刷新重试');
    }
}

/**
 * 解析URL参数
 */
function parseUrlParameters() {
    const urlParams = new URLSearchParams(window.location.search);
    const pathMatch = window.location.pathname.match(/\/authors\/([^\/]+)/);
    
    // 检查是否有作者slug
    if (pathMatch && pathMatch[1]) {
        const slug = pathMatch[1];
        AuthorsPageState.currentView = urlParams.get('view') === 'quotes' ? 'quotes' : 'detail';
        AuthorsPageState.authorSlug = slug;
    } else {
        AuthorsPageState.currentView = 'list';
    }
    
    // 解析分页参数
    AuthorsPageState.currentPage = parseInt(urlParams.get('page')) || 1;
    AuthorsPageState.pageSize = parseInt(urlParams.get('size')) || 20;
    
    // 解析搜索和排序参数
    AuthorsPageState.searchQuery = urlParams.get('search') || '';
    AuthorsPageState.sortBy = urlParams.get('sort') || 'name';
    AuthorsPageState.sortDirection = urlParams.get('order') || 'asc';
}

/**
 * 加载页面组件
 */
async function loadPageComponents() {
    // 加载导航组件
    await ComponentLoader.loadComponent('navigation-container', 'navigation');
    
    // 加载面包屑导航
    await ComponentLoader.loadComponent('breadcrumb-container', 'breadcrumb');
    
    // 加载页脚
    await ComponentLoader.loadComponent('footer-container', 'footer');
    
    // 加载侧边栏组件
    await ComponentLoader.loadComponent('popular-topics-container', 'popular-topics');
    await ComponentLoader.loadComponent('sidebar-language-switcher', 'language-switcher', {
        style: 'buttons',
        showFlags: true,
        showNames: true
    });
}

/**
 * 加载页面内容
 */
async function loadPageContent() {
    showLoadingState();
    
    try {
        switch (AuthorsPageState.currentView) {
            case 'list':
                await loadAuthorsList();
                break;
            case 'detail':
                await loadAuthorDetail();
                break;
            case 'quotes':
                await loadAuthorQuotes();
                break;
        }
    } catch (error) {
        console.error('加载页面内容失败:', error);
        showErrorMessage('内容加载失败，请稍后重试');
    } finally {
        hideLoadingState();
    }
}

/**
 * 加载作者列表
 */
async function loadAuthorsList() {
    // 显示列表视图
    showListView();
    
    // 构建查询参数
    const queryParams = {
        page: AuthorsPageState.currentPage,
        size: AuthorsPageState.pageSize,
        search: AuthorsPageState.searchQuery,
        sort: AuthorsPageState.sortBy,
        order: AuthorsPageState.sortDirection
    };
    
    // 获取作者数据
    const authorsData = await ApiClient.getAuthors(queryParams);
    
    if (authorsData && authorsData.authors) {
        // 更新页面状态
        AuthorsPageState.totalAuthors = authorsData.totalCount;
        AuthorsPageState.totalPages = authorsData.totalPages;
        
        // 渲染作者列表
        renderAuthorsList(authorsData.authors);
        
        // 更新分页
        await updatePagination(authorsData);
        
        // 更新统计信息
        updateAuthorsCount(authorsData.totalCount);
        
        // 更新页面元数据
        window.currentPageData = {
            authors: authorsData.authors,
            totalCount: authorsData.totalCount
        };
    }
}

/**
 * 加载作者详情
 */
async function loadAuthorDetail() {
    // 显示详情视图
    showDetailView();
    
    try {
        // 根据slug获取作者信息
        const author = await ApiClient.getAuthorBySlug(AuthorsPageState.authorSlug);
        
        if (author) {
            AuthorsPageState.currentAuthor = author;
            
            // 渲染作者详情
            renderAuthorDetail(author);
            
            // 获取作者的名言数量
            const quotesData = await ApiClient.getQuotesByAuthor(author.id, { page: 1, size: 1 });
            if (quotesData) {
                AuthorsPageState.totalQuotes = quotesData.totalCount;
                updateQuoteCount(quotesData.totalCount);
            }
            
            // 加载相关作者
            await loadRelatedAuthors(author);
            
            // 更新页面元数据
            window.currentPageData = {
                author: author,
                totalQuotes: AuthorsPageState.totalQuotes
            };
        } else {
            // 作者不存在，显示404
            show404Error();
        }
    } catch (error) {
        console.error('加载作者详情失败:', error);
        showErrorMessage('作者信息加载失败');
    }
}

/**
 * 加载作者名言
 */
async function loadAuthorQuotes() {
    // 显示详情视图（包含名言列表）
    showDetailView();
    showQuotesView();
    
    try {
        // 先加载作者信息
        const author = await ApiClient.getAuthorBySlug(AuthorsPageState.authorSlug);
        
        if (author) {
            AuthorsPageState.currentAuthor = author;
            renderAuthorDetail(author);
            
            // 加载作者的名言
            const quotesData = await ApiClient.getQuotesByAuthor(author.id, {
                page: AuthorsPageState.currentPage,
                size: AuthorsPageState.pageSize
            });
            
            if (quotesData && quotesData.quotes) {
                // 更新状态
                AuthorsPageState.totalQuotes = quotesData.totalCount;
                AuthorsPageState.totalPages = quotesData.totalPages;
                
                // 渲染名言列表
                await ComponentLoader.loadComponent('quotes-container', 'quotes-list', {
                    quotes: quotesData.quotes,
                    showAuthor: false // 不显示作者信息，因为已经在页面标题中
                });
                
                // 更新分页
                await updatePagination(quotesData);
                
                // 更新统计信息
                updateQuoteCount(quotesData.totalCount);
                
                // 更新页面元数据
                window.currentPageData = {
                    author: author,
                    quotes: quotesData.quotes,
                    totalQuotes: quotesData.totalCount
                };
            }
        } else {
            show404Error();
        }
    } catch (error) {
        console.error('加载作者名言失败:', error);
        showErrorMessage('名言加载失败');
    }
}

/**
 * 渲染作者列表
 */
function renderAuthorsList(authors) {
    const container = document.getElementById('authors-list-container');
    if (!container) return;
    
    let html = '<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">';
    
    authors.forEach(author => {
        const authorUrl = UrlHandler.getAuthorUrl(author);
        const quotesUrl = UrlHandler.getAuthorUrl(author, 'quotes');
        
        html += `
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 p-6">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 rounded-full bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900 dark:to-blue-800 flex items-center justify-center text-blue-600 dark:text-blue-300 font-bold text-lg mr-4">
                        ${author.name.charAt(0).toUpperCase()}
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                            <a href="${authorUrl}" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                                ${author.name}
                            </a>
                        </h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400">
                            ${author.quote_count || 0} ${I18n.t('content.quotes')}
                        </p>
                    </div>
                </div>
                
                ${author.description ? `
                    <p class="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-3">
                        ${author.description}
                    </p>
                ` : ''}
                
                <div class="flex gap-2">
                    <a href="${authorUrl}" 
                       class="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-4 rounded-lg text-center transition-colors">
                        ${I18n.t('author.profile')}
                    </a>
                    <a href="${quotesUrl}" 
                       class="flex-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 text-sm font-medium py-2 px-4 rounded-lg text-center transition-colors">
                        ${I18n.t('author.view_all_quotes')}
                    </a>
                </div>
            </div>
        `;
    });
    
    html += '</div>';
    container.innerHTML = html;
}

/**
 * 渲染作者详情
 */
function renderAuthorDetail(author) {
    // 更新作者姓名
    const nameElement = document.getElementById('author-name');
    if (nameElement) {
        nameElement.textContent = author.name;
    }
    
    // 更新作者首字母
    const initialElement = document.getElementById('author-initial');
    if (initialElement) {
        initialElement.textContent = author.name.charAt(0).toUpperCase();
    }
    
    // 更新作者描述
    const descElement = document.getElementById('author-description');
    if (descElement && author.description) {
        descElement.textContent = author.description;
    }
    
    // 更新生卒年份
    if (author.birth_year || author.death_year) {
        const birthDeathElement = document.getElementById('author-birth-death');
        const birthYearElement = document.getElementById('birth-year');
        const deathYearElement = document.getElementById('death-year');
        
        if (birthDeathElement && birthYearElement && deathYearElement) {
            birthYearElement.textContent = author.birth_year || '?';
            deathYearElement.textContent = author.death_year || '?';
            birthDeathElement.classList.remove('hidden');
        }
    }
    
    // 更新国籍
    if (author.nationality) {
        const nationalityElement = document.getElementById('author-nationality');
        const nationalityTextElement = document.getElementById('nationality');
        
        if (nationalityElement && nationalityTextElement) {
            nationalityTextElement.textContent = author.nationality;
            nationalityElement.classList.remove('hidden');
        }
    }
}

/**
 * 显示列表视图
 */
function showListView() {
    const pageHeader = document.getElementById('page-header');
    if (pageHeader) pageHeader.classList.remove('hidden');

    const authorDetailHeader = document.getElementById('author-detail-header');
    if (authorDetailHeader) authorDetailHeader.classList.add('hidden');

    const authorsListContainer = document.getElementById('authors-list-container');
    if (authorsListContainer) authorsListContainer.classList.remove('hidden');

    const quotesContainer = document.getElementById('quotes-container');
    if (quotesContainer) quotesContainer.classList.add('hidden');

    const relatedAuthorsContainer = document.getElementById('related-authors-container');
    if (relatedAuthorsContainer) relatedAuthorsContainer.classList.add('hidden');
}

/**
 * 显示详情视图
 */
function showDetailView() {
    const pageHeader = document.getElementById('page-header');
    if (pageHeader) pageHeader.classList.add('hidden');

    const authorDetailHeader = document.getElementById('author-detail-header');
    if (authorDetailHeader) authorDetailHeader.classList.remove('hidden');

    const authorsListContainer = document.getElementById('authors-list-container');
    if (authorsListContainer) authorsListContainer.classList.add('hidden');

    const relatedAuthorsContainer = document.getElementById('related-authors-container');
    if (relatedAuthorsContainer) relatedAuthorsContainer.classList.remove('hidden');
}

/**
 * 显示名言视图
 */
function showQuotesView() {
    const quotesContainer = document.getElementById('quotes-container');
    if (quotesContainer) quotesContainer.classList.remove('hidden');
}

/**
 * 更新分页
 */
async function updatePagination(data) {
    await ComponentLoader.loadComponent('pagination-container', 'pagination', {
        currentPage: AuthorsPageState.currentPage,
        totalPages: data.totalPages,
        totalCount: data.totalCount,
        onPageChange: (page) => {
            AuthorsPageState.currentPage = page;
            UrlHandler.updateQueryParam('page', page);
            loadPageContent();
        }
    });
}

/**
 * 更新作者数量显示
 */
function updateAuthorsCount(count) {
    const countElement = document.getElementById('authors-count');
    if (countElement) {
        countElement.textContent = I18n.formatNumber(count);
    }
}

/**
 * 更新名言数量显示
 */
function updateQuoteCount(count) {
    const countElement = document.getElementById('quote-count');
    if (countElement) {
        countElement.textContent = I18n.formatNumber(count);
    }
}

/**
 * 设置事件监听
 */
function setupEventListeners() {
    // 搜索功能
    const searchInput = document.getElementById('author-search');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                AuthorsPageState.searchQuery = e.target.value.trim();
                AuthorsPageState.currentPage = 1;
                UrlHandler.updateQueryParam('search', AuthorsPageState.searchQuery);
                UrlHandler.updateQueryParam('page', 1);
                loadPageContent();
            }, 500);
        });
        
        // 设置初始值
        searchInput.value = AuthorsPageState.searchQuery;
    }
    
    // 排序功能
    const sortSelect = document.getElementById('sort-select');
    if (sortSelect) {
        sortSelect.addEventListener('change', (e) => {
            AuthorsPageState.sortBy = e.target.value;
            AuthorsPageState.currentPage = 1;
            UrlHandler.updateQueryParam('sort', AuthorsPageState.sortBy);
            UrlHandler.updateQueryParam('page', 1);
            loadPageContent();
        });
        
        // 设置初始值
        sortSelect.value = AuthorsPageState.sortBy;
    }
}

/**
 * 加载相关作者
 */
async function loadRelatedAuthors(author) {
    try {
        const relatedAuthors = await ApiClient.getRelatedAuthors(author.id, { limit: 5 });
        
        if (relatedAuthors && relatedAuthors.length > 0) {
            const container = document.getElementById('related-authors-container');
            if (container) {
                let html = `
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                        <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
                            ${I18n.t('content.related')} ${I18n.t('content.author')}
                        </h3>
                        <div class="space-y-3">
                `;
                
                relatedAuthors.forEach(relatedAuthor => {
                    const authorUrl = UrlHandler.getAuthorUrl(relatedAuthor);
                    html += `
                        <div class="flex items-center">
                            <div class="w-8 h-8 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-gray-600 dark:text-gray-300 font-medium text-sm mr-3">
                                ${relatedAuthor.name.charAt(0).toUpperCase()}
                            </div>
                            <div class="flex-1">
                                <a href="${authorUrl}" class="text-sm font-medium text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                                    ${relatedAuthor.name}
                                </a>
                                <p class="text-xs text-gray-500 dark:text-gray-400">
                                    ${relatedAuthor.quote_count || 0} ${I18n.t('content.quotes')}
                                </p>
                            </div>
                        </div>
                    `;
                });
                
                html += '</div></div>';
                container.innerHTML = html;
            }
        }
    } catch (error) {
        console.error('加载相关作者失败:', error);
    }
}

/**
 * 显示加载状态
 */
function showLoadingState() {
    AuthorsPageState.isLoading = true;
    const container = document.getElementById('content-container');
    if (container) {
        container.innerHTML = `
            <div class="flex justify-center items-center py-12">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                <span class="ml-3 text-gray-600 dark:text-gray-400">${I18n.t('status.loading')}</span>
            </div>
        `;
    }
}

/**
 * 隐藏加载状态
 */
function hideLoadingState() {
    AuthorsPageState.isLoading = false;
}

/**
 * 显示错误信息
 */
function showErrorMessage(message) {
    const container = document.getElementById('content-container');
    if (container) {
        container.innerHTML = `
            <div class="text-center py-12">
                <div class="text-red-500 text-6xl mb-4">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                    ${I18n.t('error.unknown')}
                </h3>
                <p class="text-gray-600 dark:text-gray-400 mb-4">
                    ${message}
                </p>
                <button onclick="location.reload()" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                    ${I18n.t('btn.retry')}
                </button>
            </div>
        `;
    }
}

/**
 * 显示404错误
 */
function show404Error() {
    const container = document.getElementById('content-container');
    if (container) {
        container.innerHTML = `
            <div class="text-center py-12">
                <div class="text-gray-400 text-6xl mb-4">
                    <i class="fas fa-user-slash"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                    ${I18n.t('error.404')}
                </h3>
                <p class="text-gray-600 dark:text-gray-400 mb-4">
                    ${I18n.t('error.404.description')}
                </p>
                <a href="${UrlHandler.getListUrl('authors')}" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                    ${I18n.t('btn.back')} ${I18n.t('nav.authors')}
                </a>
            </div>
        `;
    }
}

/**
 * 更新页面元数据
 */
function updatePageMetadata() {
    // 触发meta标签更新
    if (typeof MetaTags !== 'undefined') {
        MetaTags.updatePageMeta(window.currentPageData);
    }
    
    // 触发结构化数据更新
    if (typeof StructuredData !== 'undefined') {
        StructuredData.autoGenerate(window.currentPageData);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', initAuthorsPage);

// 监听路由变化
document.addEventListener('routeChanged', () => {
    parseUrlParameters();
    loadPageContent();
});
